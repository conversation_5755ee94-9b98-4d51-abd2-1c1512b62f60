.container {
  padding-right: 0;
  padding-left: 0;
}

.common-title {
  margin-bottom: 30px;
  color: @dark-title-color;
  font-weight: 600;
  font-size: 42px;
  font-family: Poppins;
  line-height: 40px;
  letter-spacing: 2px;
  text-align: center;
}

.common-subtitle {
  margin-bottom: 60px;
  color: @dark-text-color;
  font-size: 18px;
  line-height: 36px;
  text-align: center;
}

.link-loading {
  cursor: wait !important;
}

@media (min-width: @max-screen-size) {
  .container {
    max-width: 1460px !important;
  }
}

@media (max-width: @large-screen-size) {
  .common-title {
    font-size: 32px;
  }
}

@media (max-width: @mini-screen-size) {
  .container {
    padding-right: 20px;
    padding-left: 20px;
  }
}
