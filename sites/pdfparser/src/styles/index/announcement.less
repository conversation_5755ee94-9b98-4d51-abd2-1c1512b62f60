.announcement-wrapper {
  background-color: #24272a;

  .announcement-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 50px;
    text-align: center;
  }

  .announcement-content {
    line-height: 1;
  }

  .announcement-logo-box {
    display: inline;
  }

  .announcement-logo {
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }

  .announcement-text {
    display: inline;
    color: #fff;
    font-size: 16px;
    line-height: 22px;
  }

  .announcement-link {
    margin-left: 24px;
    color: #88cfff;
    font-weight: 500;
    font-family: Poppins;
    cursor: pointer;
    text-underline-offset: 1.5px;

    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: @small-screen-size) {
  .announcement-wrapper {
    .announcement-logo {
      width: 48px;
      height: 25px;
      margin-right: 6px;
    }

    .announcement-text {
      font-size: 14px;
      line-height: 20px;
    }

    .announcement-link {
      margin-left: 12px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .announcement-wrapper {
    .announcement-logo {
      width: 29px;
      height: 15px;
    }

    .announcement-text {
      font-size: 12px;
    }
  }
}
