.banner-wrapper {
  position: relative;
  height: 950px;

  .banner-bg {
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-color: #000;
  }

  .banner-container {
    display: flex;
    gap: 100px;
    align-items: center;
    padding-top: 186px;
  }

  .text-content {
    display: flex;
    flex: 2;
    flex-direction: column;
    justify-content: center;
  }

  .upload-content {
    position: relative;
    z-index: 10;
    flex: 1;
    box-sizing: border-box;
    min-width: 600px;
    height: 400px;
    padding: 16px 20px;
    background: #fff;
    border-radius: 16px;
    backdrop-filter: blur(10px);
  }

  .ant-upload-drag {
    width: 100%;
    height: 100%;
    background: #e9ebfb;
    border: 2px dashed #a196ff;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.1s;

    &.ant-upload-drag-hover,
    &:hover {
      background: linear-gradient(180deg, #c8ceff 0%, #e9ebfb 100%);
      border-color: #a196ff;

      .text {
        color: #6576db;
        background: #bdc5ff;
      }

      .sub-text {
        color: #6576db;
      }
    }

    .ant-upload-btn {
      display: table;
      width: 100%;
      height: 100%;
    }

    .ant-upload-drag-container {
      display: table-cell;
      vertical-align: middle;
    }

    .icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0 20px;
    }

    .text {
      box-sizing: border-box;
      width: 250px;
      height: 64px;
      margin-bottom: 10px;
      padding: 0 32px;
      color: #6576db;
      font-weight: 600;
      font-size: 24px;
      font-family: Poppins;
      line-height: 64px;
      text-align: center;
      background: #dbdfff;
      border-radius: 32px;
    }

    .sub-text {
      color: #6a6b76;
      font-weight: 500;
      font-size: 18px;
      font-family: Poppins;
      text-align: center;
    }

    .upload-info {
      box-sizing: border-box;
      width: 100%;
      padding: 20px;
      text-align: left;
      background: #fff;
      border-radius: 12px;

      .file-name {
        max-width: 470px;
        margin-bottom: 10px;
        overflow: hidden;
        color: #0b0b0b;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .banner-title {
    margin-bottom: 36px;
    color: #fff;
    font-weight: 700;
    font-size: 64px;
    font-family: Poppins;
    line-height: 80px;
  }

  .banner-text {
    &.first {
      background: linear-gradient(90deg, #8ca7ff 0%, #bdcfff 25.38%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.third {
      background: linear-gradient(90deg, #d4dfff 62.04%, #94aeff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .banner-desc {
    margin-bottom: 72px;
    color: #fff;
    font-weight: 400;
    font-size: 26px;
    line-height: 42px;
  }

  .banner-btns {
    position: relative;
    z-index: 10;
    display: flex;
    gap: 60px;
  }

  .api-reference-button {
    width: 280px;
    height: 64px;
    font-size: 20px;
    line-height: 64px;
    text-align: center;
    background: #fff;
    border-radius: 32px;
    cursor: pointer;

    .text {
      font-weight: 500;
      font-family: Poppins;
      background: linear-gradient(
        220deg,
        rgba(157, 157, 249, 90%) 3.61%,
        rgba(29, 69, 194, 90%) 87.27%
      );
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .banner-video {
    position: relative;
    width: 80%;
    max-width: 1130px;

    &::after {
      position: absolute;
      top: -10px;
      left: -10px;
      z-index: -1;
      width: calc(100% + 20px);
      height: calc(100% + 20px);
      background-color: #fff;
      border: @border-base;
      border-radius: 20px;
      box-shadow: 0 1.495px 5.982px 0 rgba(0, 0, 0, 12%);
      opacity: 0.6;
      content: '';
    }

    .xgplayer-container {
      border-radius: 20px;

      .xgplayer,
      .poster,
      video {
        border-radius: 20px;
      }
    }
  }
}

@media (max-width: @large-screen-size) {
  .banner-wrapper {
    height: 600px;

    .banner-container {
      gap: 60px;
      padding-top: 120px;
    }

    .upload-content {
      min-width: 500px;
      height: 330px;
    }

    .ant-upload-drag {
      .text {
        width: 220px;
        height: 56px;
        font-size: 22px;
        line-height: 56px;
      }

      .upload-info {
        .file-name {
          max-width: 370px;
        }
      }
    }

    .banner-title {
      margin-bottom: 20px;
      font-size: 50px;
      line-height: 60px;
    }

    .banner-desc {
      margin-bottom: 50px;
      font-size: 24px;
      line-height: 36px;
    }

    .banner-btns {
      gap: 40px;
    }

    .banner-video {
      width: 80%;
    }

    .api-reference-button {
      width: 240px;
      height: 54px;
      font-size: 18px;
      line-height: 54px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .banner-wrapper {
    .banner-container {
      gap: 30px;
    }

    .banner-title {
      margin-bottom: 18px;
      font-size: 46px;
      line-height: 54px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .banner-wrapper {
    height: 480px;

    .text-content {
      align-items: center;
    }

    .upload-content {
      display: none;
    }

    .banner-title {
      margin-bottom: 16px;
      font-size: 40px;
      line-height: 50px;
      text-align: center;
    }

    .banner-desc {
      margin-bottom: 40px;
      font-size: 20px;
      line-height: 30px;
      text-align: center;
    }

    .banner-container {
      padding-top: 80px;
    }

    .api-reference-button {
      width: 180px;
      height: 42px;
      font-size: 16px;
      line-height: 42px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .banner-wrapper {
    height: 360px;

    .banner-container {
      padding-top: 76px;
    }

    .banner-title {
      margin-bottom: 12px;
      font-weight: 500;
      font-size: 26px;
      line-height: normal;
    }

    .banner-desc {
      margin-bottom: 24px;
      font-weight: 500;
      font-size: 13px;
      font-family: Poppins;
      line-height: normal;
    }

    .banner-btns {
      flex-direction: column;
      gap: 20px;
    }

    .banner-video {
      width: 100%;

      &::after {
        top: -5px;
        left: -5px;
        width: calc(100% + 10px);
        height: calc(100% + 10px);
      }
    }

    .api-reference-button {
      font-weight: 400;
      font-size: 14px;
    }
  }
}
