.header-wrapper {
  position: sticky;
  top: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  height: 60px;
  background-color: #fff;

  &.shadow {
    box-shadow:
      0 -1px #fff,
      0 0.5rem 1rem rgba(0, 0, 0, 15%) !important;
  }

  .header-navbar {
    display: flex;
    justify-content: space-between;
  }

  .chatdoc-brand {
    display: flex;
    align-items: center;
  }

  .chatdoc-logo {
    width: 40px;
  }

  .chatdoc-name {
    margin-left: 10px;
    color: #000;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 22px;
  }

  .nav-right {
    flex-direction: row;
    gap: 30px;
    align-items: center;
  }

  .nav-link {
    padding: 0 !important;
    color: @dark-title-color;
    font-weight: 500;
    font-size: 16px;
    font-family: Poppins;
    line-height: 24px;
    white-space: nowrap;

    &:hover {
      color: #6576db;
    }
  }

  .api-dropdown-menu {
    .dropdown-menu {
      position: absolute;
      margin-top: 0;
      padding-top: 15px;
      background-color: transparent;
      border: none;
    }

    .dropdown-toggle {
      &::after {
        content: unset;
      }
    }

    &:hover {
      .dropdown-menu {
        display: block;
      }
    }
  }

  .api-menu-content {
    display: flex;
    flex-direction: column;
    padding: 30px;
    background-color: #fff;
    border: @border-base;
    border-radius: 4px;
    box-shadow: 0 20px 38px 0 rgba(101, 122, 147, 20%);
    row-gap: 30px;
  }

  .api-menu-item {
    color: @dark-color;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 26px;
    white-space: nowrap;
    cursor: pointer;

    &:hover {
      color: #6576db;
    }
  }
}

@media (max-width: @large-screen-size) {
  .header-wrapper {
    .api-dropdown-menu {
      .dropdown-menu {
        padding-top: 8px;
      }
    }

    .api-menu-content {
      position: absolute;
      left: -5px;
      padding: 20px;
      row-gap: 20px;
    }

    .api-menu-item {
      font-size: 16px;
      line-height: 20px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .header-wrapper {
    height: 65px;

    .chatdoc-logo {
      width: 35px;
    }

    .chatdoc-name {
      font-size: 14px;
      line-height: 16px;
    }

    .nav-link {
      font-size: 14px;
    }
  }
}
