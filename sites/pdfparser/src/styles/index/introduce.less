.introduce-wrapper {
  box-sizing: border-box;
  padding: 120px 0;
  background: #fcfcff;
  border-bottom: 1px solid #dedede;

  .introduce-title {
    margin-bottom: 120px;
  }

  .introduce-list {
    .introduce-item {
      display: flex;
      gap: 100px;
      justify-content: center;
      margin-bottom: 70px;

      &:last-child {
        margin-bottom: 0;
      }

      &:nth-child(2n) {
        flex-direction: row-reverse;
      }

      .content {
        display: flex;
        flex-direction: column;
        gap: 24px;
        justify-content: center;
        max-width: 600px;

        .title-wrapper {
          display: flex;
          gap: 28px;
          align-items: center;

          .title-img-wrapper {
            width: 94px;
          }

          span {
            color: #605c85;
            font-weight: 600;
            font-size: 42px;
            font-family: Poppins;
          }
        }

        .desc {
          color: #707070;
          font-weight: 400;
          font-size: 32px;
          font-style: normal;
          line-height: 54px;
        }
      }

      .img-wrapper {
        width: 600px;
      }
    }
  }
}

@media (max-width: @large-screen-size) {
  .introduce-wrapper {
    padding: 100px 0;

    .introduce-title {
      margin-bottom: 80px;
    }

    .introduce-list {
      .introduce-item {
        gap: 80px;

        .content {
          max-width: 500px;

          .title-wrapper {
            .title-img-wrapper {
              width: 80px;
            }

            span {
              font-size: 32px;
            }
          }

          .desc {
            font-size: 24px;
            line-height: 46px;
          }
        }

        .img-wrapper {
          width: 500px;
        }
      }
    }
  }
}

@media (max-width: @middle-screen-size) {
  .introduce-wrapper {
    .introduce-list {
      .introduce-item {
        .content {
          max-width: 400px;

          .title-wrapper {
            .title-img-wrapper {
              width: 60px;
            }

            span {
              font-size: 26px;
            }
          }

          .desc {
            font-size: 20px;
            line-height: 32px;
          }
        }

        .img-wrapper {
          width: 400px;
        }
      }
    }
  }
}

@media (max-width: @small-screen-size) {
  .introduce-wrapper {
    background: #f7f8fc;

    .introduce-list {
      .introduce-item {
        &:not(&:last-child) {
          margin-bottom: 20px;
        }

        flex-direction: column !important;
        gap: 16px;
        padding: 20px 16px;
        background: #fff;
        border-radius: 12px;

        .content {
          gap: 20px;
          max-width: 100%;

          .title-wrapper {
            gap: 10px;

            .title-img-wrapper {
              width: 42px;
            }

            span {
              font-size: 24px;
            }
          }

          .desc {
            font-size: 18px;
            line-height: 32px;
          }
        }

        .img-wrapper {
          display: flex;
          justify-content: center;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .introduce-wrapper {
    padding: 54px 0;

    .introduce-title {
      margin-bottom: 42px;
    }

    .introduce-list {
      .introduce-item {
        gap: 12px;

        .content {
          gap: 16px;

          .title-wrapper {
            gap: 8px;

            span {
              font-weight: 500;
              font-size: 20px;
            }
          }

          .desc {
            font-size: 16px;
          }
        }
      }
    }
  }
}
