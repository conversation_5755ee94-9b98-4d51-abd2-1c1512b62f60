.service-wrapper {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 120px 0;
  background: #fff;
  border-bottom: 1px solid #dedede;

  .service-title {
    margin-bottom: 120px;
  }

  .service-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 80px 40px;
    align-items: center;
    justify-content: space-between;
  }

  .service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .service-image {
    width: 212px;
    margin-bottom: 24px;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.2);
    }
  }

  .service-info {
    color: @dark-text-color;
    font-weight: 500;
    font-size: 24px;
    font-family: Poppins;
    line-height: 40px;
    letter-spacing: 2px;
    white-space: pre-wrap;
    text-align: center;
  }
}

@media (max-width: @large-screen-size) {
  .service-wrapper {
    padding: 100px 0;

    .service-title {
      margin-bottom: 80px;
    }

    .service-image {
      width: 160px;
    }

    .service-info {
      font-size: 18px;
      line-height: 32px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .service-wrapper {
    .service-content {
      grid-template-columns: 1fr 1fr;
    }
  }
}

@media (max-width: @small-screen-size) {
  .service-wrapper {
    .service-image {
      margin-bottom: 10px;
    }

    .content-right {
      gap: 40px 0;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .service-wrapper {
    padding: 54px 0;

    .service-title {
      margin-bottom: 42px;
    }

    .service-container {
      padding-top: 40px;
    }

    .service-content {
      grid-template-columns: 1fr;
    }

    .service-info {
      font-weight: 500;
      font-family: Poppins;
    }
  }
}
