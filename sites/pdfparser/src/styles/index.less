.pdfparser-index-page {
  font-weight: 400;
  font-family: Pop<PERSON><PERSON>;

  --toastify-toast-width: auto;

  .Toastify__toast-container.Toastify__toast-container--top-center {
    left: 50%;
    width: var(--toastify-toast-width);
    margin: 16px auto;
    transform: translateX(-50%);
  }

  .Toastify__toast-body {
    cursor: default;
  }

  .link-footer-wrapper {
    background-color: #f3f6fa;
  }

  // footer 组件不需要调整小屏幕下的样式，通过以下样式覆盖小屏幕的响应式样式。
  @media (max-width: @mini-screen-size) {
    .common-title {
      font-weight: 500;
      font-size: 24px;
      letter-spacing: 1px;
    }

    &.has-announcement {
      .animation-card-wrapper {
        top: 390px;
      }
    }

    .footer-wrapper {
      padding-bottom: 20px;

      .footer-link {
        font-size: 14px;
        line-height: normal;
      }

      .footer-point {
        padding: 0 3px;
        font-size: 14px;
      }

      .footer-bg-mini {
        height: 500px;
      }
    }
  }
}

@import './components/common.less';
@import './components/animation-button.less';
@import './index/announcement.less';
@import './index/header.less';
@import './index/banner.less';
@import './index/animation-card.less';
@import './index/service.less';
@import './index/introduce.less';
@import './index/feature.less';
@import './index/preview.less';
@import './index/link.less';
@import './index/footer.less';
