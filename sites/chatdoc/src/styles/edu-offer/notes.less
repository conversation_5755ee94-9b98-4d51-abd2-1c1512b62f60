.edu-offer-notes-wrapper {
  padding: 120px 0;
  background: #fff;

  .notes-title {
    margin-bottom: 40px;
    color: #142132;
    font-weight: 500;
    font-size: 40px;
    font-family: Poppins;
    line-height: 72px;
    letter-spacing: 2px;
  }

  .note-list {
    padding-left: 20px;
    color: #142132;
    font-size: 18px;
    font-style: normal;
    line-height: 38px;
    letter-spacing: 2px;

    .note-item {
      margin-bottom: 20px;
      list-style: disc;

      &:last-child {
        margin-bottom: 0;
      }

      &::marker {
        color: #6071d7;
      }

      .chatdoc-email {
        color: #6071d7;
        font-weight: 500;
        font-family: Poppins;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

@media (max-width: 1400px) {
  .edu-offer-notes-wrapper {
    padding: 80px 0;

    .notes-title {
      margin-bottom: 30px;
      font-size: 30px;
    }

    .note-list {
      font-size: 16px;
      line-height: 32px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .edu-offer-notes-wrapper {
    padding: 50px 0;

    .notes-title {
      margin-bottom: 30px;
      font-weight: 500;
      font-size: 20px;
      line-height: 32px;
    }

    .note-list {
      font-size: 12px;
      line-height: 20px;

      .note-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .chatdoc-email {
          font-weight: 500;
        }
      }
    }
  }
}
