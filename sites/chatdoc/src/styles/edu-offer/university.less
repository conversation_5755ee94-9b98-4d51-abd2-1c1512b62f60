.edu-offer-university-wrapper {
  .university-title {
    margin-top: 120px;
    color: #142132;
    font-weight: 500;
    font-size: 36px;
    font-family: Poppins;
    line-height: 48px;
    letter-spacing: 2px;
    text-align: center;
  }

  .university-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px 80px;
    justify-content: space-between;
    padding: 80px 100px 140px;
  }

  .university-logo {
    height: 100%;
  }
}

@media (max-width: @max-screen-size) {
  .edu-offer-university-wrapper {
    .university-logo {
      max-width: 200px;
    }
  }
}

@media (max-width: 1400px) {
  .edu-offer-university-wrapper {
    .university-title {
      font-size: 30px;
    }

    .university-logo {
      max-width: 160px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .edu-offer-university-wrapper {
    .university-content {
      justify-content: center;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .edu-offer-university-wrapper {
    .university-title {
      margin-top: 42px;
      font-weight: 500;
      font-size: 16px;
      line-height: 32px;
    }

    .university-content {
      gap: 30px 40px;
      padding: 30px 0 48px;
    }

    .university-item {
      max-width: 40%;
    }

    .university-logo {
      max-width: 100%;
    }
  }
}
