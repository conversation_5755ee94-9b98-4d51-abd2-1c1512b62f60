.edu-offer-faqs-wrapper {
  position: relative;
  z-index: 1;
  padding: 90px 0 180px;
  background: rgba(233, 244, 255, 60%);

  .faqs-title {
    color: #142132;
    font-weight: 500;
    font-size: 40px;
    font-family: Poppins;
    line-height: 72px;
    letter-spacing: 2px;
    text-align: center;
  }

  .faqs-sub-title {
    margin: 40px 0;
    color: #2f3136;
    font-size: 20px;
    line-height: 36px;
    text-align: center;
  }

  .list {
    background: #fff;
    border-radius: 10px;

    .item {
      display: flex;
      flex-wrap: wrap;
      gap: 50px;
      box-sizing: border-box;
      padding: 40px 50px;
      border-bottom: 2px solid #f0f3f4;

      .left {
        color: #6974de;
        font-weight: 700;
        font-size: 40px;
        font-family: Poppins;
        line-height: 120%;
      }

      .content {
        flex: 1;

        .question {
          color: #142132;
          font-weight: 500;
          font-size: 24px;
          font-family: Poppins;
          line-height: 42px;
        }

        .answer {
          margin-top: 24px;
          color: #707479;
          font-size: 18px;
        }
      }
    }
  }

  .faqs-bg {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
  }
}

@media (max-width: 1400px) {
  .edu-offer-faqs-wrapper {
    padding: 60px 0 140px;

    .faqs-title {
      font-size: 30px;
    }

    .faqs-sub-title {
      margin: 30px 0;
      font-size: 18px;
    }

    .list {
      .item {
        gap: 36px;

        .left {
          font-size: 30px;
        }

        .content {
          .question {
            font-size: 20px;
          }

          .answer {
            font-size: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .edu-offer-faqs-wrapper {
    padding: 26px 0 110px;

    .faqs-title {
      font-weight: 500;
      font-size: 20px;
      line-height: 32px;
    }

    .faqs-sub-title {
      margin: 10px 0 16px;
      font-size: 12px;
      line-height: 20px;
    }

    .list {
      .item {
        flex-wrap: nowrap;
        gap: 14px;
        padding: 24px;

        .left {
          font-weight: 500;
          font-size: 16px;
          line-height: 120%;
        }

        .content {
          flex: initial;
          width: 100%;

          .question {
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
          }

          .answer {
            margin-top: 8px;
            font-size: 12px;
            line-height: 16px;
          }
        }
      }
    }
  }
}
