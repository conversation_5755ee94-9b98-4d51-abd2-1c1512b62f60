.edu-offer-discount-code-wrapper {
  padding: 120px 0;

  .discount-code-title {
    margin-bottom: 60px;
    color: #142132;
    font-weight: 500;
    font-size: 40px;
    font-family: Poppins;
    line-height: 72px;
    letter-spacing: 2px;
  }

  .step-item {
    position: relative;
    padding-left: 80px;

    &.step2 {
      padding-bottom: 40px;
    }

    &.step3 {
      &::before {
        display: none;
      }
    }

    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 14px;
      width: 2px;
      height: 100%;
      background: rgba(105, 116, 222, 30%);
      content: '';
    }

    .dot {
      position: absolute;
      top: -4px;
      left: 0;
      z-index: 1;
      width: 30px;
      height: 30px;
      color: #fff;
      font-size: 20px;
      line-height: 30px;
      text-align: center;
      background: #6974de;
      border-radius: 50%;
    }

    .text {
      padding-bottom: 40px;
      color: #142132;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: 2px;
    }

    .discount {
      color: #ff7d1a;
      font-weight: 700;
      font-size: 24px;
      font-family: Poppins;
    }

    .extra-list {
      display: flex;
      flex-wrap: wrap;
      gap: 40px;
      box-sizing: border-box;
      padding: 30px 20px;
      background: #fff;
      border: 1px solid #e7e9eb;
      border-radius: 20px;

      .extra-item {
        flex: 1;
        min-width: 280px;
        text-align: center;

        .step-img {
          height: 310px;
        }

        .step-text {
          color: #646464;
          font-size: 16px;
          line-height: 36px;
        }

        .extra-text {
          color: #6576db;
          font-weight: 500;
          font-size: 18px;
          font-family: Poppins;
          line-height: 36px;
          list-style: circle;
        }
      }
    }

    &:last-child {
      .text {
        padding-bottom: 0;
      }
    }
  }
}

@media (max-width: 1400px) {
  .edu-offer-discount-code-wrapper {
    padding: 80px 0;

    .discount-code-title {
      margin-bottom: 40px;
      font-size: 30px;
    }

    .step-item {
      .dot,
      .text {
        font-size: 18px;
      }

      .step-text {
        font-size: 14px;
      }

      .discount {
        font-size: 22px;
      }

      &:not(&:last-child) .text {
        padding-bottom: 30px;
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .edu-offer-discount-code-wrapper {
    padding: 50px 0;

    .discount-code-title {
      font-weight: 500;
      font-size: 20px;
      line-height: 32px;
      white-space: pre-wrap;
      text-align: center;
    }

    .step-item {
      padding-left: 40px;

      .dot {
        top: 0;
        left: 5px;
        width: 20px;
        height: 20px;
        font-size: 12px;
        line-height: 20px;
      }

      &:not(&:last-child) .text {
        padding-bottom: 26px;
      }

      .text {
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 1px;
      }

      .step-text {
        font-size: 14px;
      }

      .discount {
        font-weight: 500;
        font-size: 16px;
      }

      .extra-list {
        gap: 16px;
        padding: 16px;

        .extra-item {
          .step-img {
            height: 200px;
          }

          .step-text {
            margin: 4px 0;
            font-size: 10px;
            line-height: 14px;
          }

          .extra-text {
            font-weight: 500;
            font-size: 12px;
            line-height: 20px;
          }
        }
      }
    }
  }
}
