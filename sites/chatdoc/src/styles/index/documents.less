.documents-wrapper {
  --document-desc-line-height: 32px;
  --document-card-margin-bottom: 30px;
  --document-card-hover-bottom: 20px;
  --document-hover-prompt-bottom: 20px;
  --document-info-padding-bottom: 15px;

  padding: 120px 0 57px;
  border-bottom: @border-base;

  .documents-subtitle {
    margin-bottom: 70px;
    white-space: nowrap;
  }

  .document-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .document-content-swiper {
    display: none;
  }

  .document-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      .document-card,
      .document-info {
        transform: translateY(calc(-1 * var(--document-card-hover-bottom)));
        transition: transform 0.5s;
      }

      .document-card {
        margin-bottom: calc(
          var(--document-card-margin-bottom) - var(--document-card-hover-bottom)
        );
        padding-bottom: var(--document-card-hover-bottom);

        &::before {
          position: absolute;
          bottom: var(--document-card-hover-bottom);
          z-index: 1;
          width: 100%;
          height: calc(100% - var(--document-card-hover-bottom));
          background-image: linear-gradient(
            180deg,
            rgba(109, 130, 250, 0%) 0%,
            rgba(109, 130, 250, 60%) 100%
          );
          border-radius: 40px;
          content: '';
          pointer-events: none;
        }
      }

      .document-hover-prompt {
        display: flex;
      }

      .document-info {
        color: #6576db;
      }

      .document-desc {
        display: block;
        animation: slide-in 0.5s;
      }
    }
  }

  .document-card {
    margin-bottom: var(--document-card-margin-bottom);
    border: none;
    border-radius: 40px;
  }

  .document-card,
  .document-info {
    transform: translateY(0);
    transition: transform 0.5s;
  }

  .document-link {
    cursor: pointer;
  }

  .document-link,
  .document-image {
    border-radius: inherit;
  }

  .document-image {
    z-index: 0;
    width: 245px;
    border: 1px solid #6576db;
    box-shadow: 0 20px 38px rgba(76, 101, 251, 20%);
    pointer-events: none;
  }

  .document-hover-prompt {
    position: absolute;
    bottom: calc(
      var(--document-hover-prompt-bottom) + var(--document-card-hover-bottom)
    );
    z-index: 2;
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .prompt-text {
    margin-right: 8px;
    color: #fff;
    font-weight: 700;
    font-size: 20px;
    font-family: Poppins;
    line-height: 36px;
  }

  .prompt-arrow {
    height: 16px;
  }

  .document-info {
    padding-bottom: var(--document-info-padding-bottom);
    color: @dark-title-color;
    font-weight: 500;
    font-size: 22px;
    font-family: Poppins;
    line-height: 33px;
    letter-spacing: 2px;
  }

  .document-desc {
    position: absolute;
    bottom: calc(
      -1 * (var(--document-desc-line-height) -
            var(--document-info-padding-bottom))
    );
    display: none;
    color: @dark-text-color;
    font-size: 18px;
    line-height: var(--document-desc-line-height);
    white-space: nowrap;
    text-align: center;
    text-shadow: 0 10px 35px rgba(53, 16, 150, 10%);
  }
}

@keyframes slide-in {
  from {
    transform: translate(0, 20px);
  }

  to {
    transform: translate(0, 0);
  }
}

@media (max-width: @large-screen-size) {
  .documents-wrapper {
    .document-content {
      justify-content: space-around;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .documents-wrapper {
    --document-desc-line-height: 24px;
    --document-card-hover-bottom: 10px;

    .document-content {
      justify-content: space-between;
    }

    .document-image {
      width: 200px;
    }

    .prompt-text {
      font-size: 18px;
      line-height: 32px;
    }

    .document-info {
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 1px;
    }

    .document-desc {
      bottom: calc(
        -1 * (2 * (var(--document-desc-line-height)) -
              var(--document-info-padding-bottom))
      );
      font-size: 16px;
      white-space: pre;
    }
  }
}

@media (max-width: @small-screen-size) {
  .documents-wrapper {
    --document-desc-line-height: 21px;
    --document-hover-prompt-bottom: 15px;

    padding-top: 92px;

    .documents-subtitle {
      margin-bottom: 30px;
    }

    .document-image {
      width: 170px;
    }

    .document-item {
      &:hover {
        .document-card {
          &::before {
            border-radius: 26px;
          }
        }
      }
    }

    .document-card {
      border-radius: 26px;
    }

    .prompt-text {
      margin-right: 5px;
      font-size: 14px;
      line-height: 20px;
    }

    .prompt-arrow {
      height: 12px;
    }

    .document-info {
      font-size: 16px;
      line-height: 24px;
    }

    .document-desc {
      font-size: 14px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .documents-wrapper {
    padding-bottom: 30px;

    .document-content {
      display: none;
    }

    .document-content-swiper {
      display: block;

      .swiper-pagination {
        margin-top: 62px;
      }
    }

    .document-item {
      &:hover {
        .document-card,
        .document-info {
          transform: none;
          transition: none;
        }

        .document-desc {
          animation: normal;
        }
      }
    }

    .document-card,
    .document-info {
      transform: none;
      transition: none;
    }

    .document-image {
      width: 156px;
    }

    .prompt-text {
      font-size: 13px;
      line-height: 18px;
    }

    .document-info {
      padding-bottom: 0;
      font-size: 18px;
      line-height: 27px;
    }

    .document-desc {
      bottom: calc(-1 * var(--document-desc-line-height));
      display: block;
      white-space: nowrap;
    }
  }
}
