.extension-wrapper {
  position: relative;
  padding: 120px 0;
  border-bottom: 1px solid #dedede;

  .extension-bg,
  .extension-bg-mini {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
  }

  .extension-bg-mini {
    display: none;
  }

  .extension-container {
    display: flex;
  }

  .extension-left,
  .extension-right {
    width: 50%;
  }

  .extension-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    column-gap: 20px;
  }

  .extension-title-text {
    color: @dark-title-color;
    font-weight: 600;
    font-size: 36px;
    font-family: Poppins;
    line-height: 40px;
    letter-spacing: 2px;
    white-space: nowrap;
  }

  .extension-title-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
  }

  .desc-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 30px;
    }
  }

  .desc-item-text {
    color: @dark-text-color;
    font-size: 18px;
    line-height: 36px;
  }

  .check-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }

  .extension-right {
    position: relative;
    display: flex;
    justify-content: flex-end;
  }

  .extension-image {
    position: absolute;
    z-index: -1;
    max-width: 656px;
  }
}

@media (max-width: @large-screen-size) {
  .extension-wrapper {
    .extension-title {
      margin-bottom: 50px;
    }

    .extension-title-text {
      font-size: 32px;
      line-height: 34px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .extension-wrapper {
    padding: 120px 0 20px;

    .extension-bg {
      display: none;
    }

    .extension-bg-mini {
      display: block;
    }

    .extension-container {
      flex-direction: column;
      align-items: center;
    }

    .extension-left,
    .extension-right {
      width: 100%;
    }

    .extension-right {
      display: unset;
    }

    .extension-title {
      margin-bottom: 24px;
    }

    .extension-title-text {
      white-space: normal;
    }

    .check-icon {
      width: 20px;
      height: 20px;
    }

    .desc-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 20px;
      }
    }

    .extension-image {
      position: relative;
      bottom: 50px;
      left: 20px;
      width: 90%;
      max-width: unset;
    }
  }
}

@media (max-width: @small-screen-size) {
  .extension-wrapper {
    padding: 90px 0 20px;

    .extension-title-text {
      font-size: 30px;
      line-height: 32px;
    }

    .extension-image {
      left: 0;
      width: 100%;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .extension-wrapper {
    padding: 60px 0 20px;

    .extension-title {
      flex-direction: column;
      align-items: normal;
    }

    .extension-title-text {
      font-size: 24px;
      line-height: 36px;
      letter-spacing: 0.545455px;
    }

    .extension-title-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 16px;
    }

    .desc-item {
      align-items: flex-start;
    }

    .desc-item-text {
      font-size: 14px;
      line-height: 16px;
    }

    .check-icon {
      width: 16px;
      height: 16px;
    }

    .extension-image {
      bottom: 20px;
    }
  }
}
