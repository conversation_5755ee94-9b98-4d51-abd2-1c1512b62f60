.faqs-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  padding-top: 90px;

  .faqs-container {
    padding: 0 !important;
  }

  .faqs-subtitle {
    color: @dark-text-color;
  }

  .faqs-cards {
    width: 100%;
  }

  .faqs-cards-mobile {
    display: none;
  }

  .faqs-cards-container {
    display: flex;
    height: 680px;
    column-gap: 40px;
  }

  .faqs-cards-right,
  .faqs-cards-left {
    display: flex;
    flex: 1 1;
    flex-direction: column;
    row-gap: 40px;
  }

  .faqs-card {
    background-color: #fff;
    border: @border-base;
    border-radius: 40px;
    box-shadow: 0 20px 38px rgba(101, 122, 147, 10%);

    &.show {
      padding-bottom: 30px;
    }

    &:hover {
      background-color: #f5f5f5;

      .faqs-answer {
        background-color: #f5f5f5;
      }
    }
  }

  .faqs-question {
    cursor: pointer;

    &::after {
      content: unset;
    }

    &[aria-expanded='true'] {
      .question-text {
        padding-bottom: 20px;

        &::before {
          vertical-align: unset !important;
          transform: rotate(-90deg);
        }
      }
    }
  }

  .question-text {
    padding: 30px;
    color: @dark-title-color;
    font-weight: 700;
    font-size: 24px;
    font-family: Poppins;
    line-height: 42px;

    &::before {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 20px;
      vertical-align: middle;
      background: url('../../assets/images/arrow-down.svg') no-repeat;
      background-size: contain;
      content: '';
    }
  }

  .faqs-answer {
    position: relative !important;
    z-index: 1;
    width: 100%;
    padding: 0 30px;
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
    border: none;
    transform: none !important;
  }
}

@media (max-width: @middle-screen-size) {
  .faqs-wrapper {
    .faqs-cards-left,
    .faqs-cards-right {
      display: none;
    }

    .faqs-cards-mobile {
      display: flex;
      flex-direction: column;
      width: 100%;
      row-gap: 20px;
    }

    .faqs-cards-container {
      height: 740px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .faqs-wrapper {
    padding-top: 60px;

    .faqs-title,
    .faqs-subtitle {
      padding: 0 20px;
    }

    .faqs-card {
      border-radius: 20px;

      &.show {
        padding-bottom: 15px;
        border-radius: 20px;
      }
    }

    .question-text {
      display: flex;
      padding: 15px 0 15px 18px;
      font-size: 16px;
      line-height: 20px;
      white-space: pre-wrap;

      &::before {
        width: 12px;
        height: 12px;
        margin-top: 6px;
        margin-right: 10px;
      }
    }

    .faqs-question {
      &[aria-expanded='true'] {
        .question-text {
          padding-bottom: 10px;

          &::before {
            margin-top: 4px;
          }
        }
      }
    }

    .faqs-answer {
      padding: 0 15px;
      font-size: 14px;
      line-height: 20px;
    }

    .faqs-cards-container {
      height: 460px;
    }
  }
}
