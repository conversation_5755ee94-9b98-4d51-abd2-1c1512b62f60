.chatdoc-index-page {
  font-weight: 400;
  font-family: <PERSON><PERSON><PERSON>;
  background-color: #000;
  background-image: url('../assets/images/page-bg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;

  .banner-wrapper {
    .banner-button {
      margin-bottom: 0;
    }

    @media (max-width: @mini-screen-size) {
      .banner-product-hunt {
        top: 13px;
      }
    }
  }

  .faqs-footer-wrapper {
    background-image: linear-gradient(180deg, #f6faff, #eaf3ff);
  }

  .module-title {
    font-weight: 600;
    font-size: 68px;
    line-height: 70px;
    background: linear-gradient(
      88deg,
      #fff 2.84%,
      rgba(255, 255, 255, 80%) 76.02%
    );
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
@media (min-width: @max-screen-size) {
  .chatdoc-index-page {
    .container {
      max-width: 1280px !important;
    }
  }
}

@media (min-width: @large-screen-size) {
  .chatdoc-index-page {
    .container {
      max-width: 1280px !important;
    }
  }
}
@media (max-width: @mini-screen-size) {
  .chatdoc-index-page {
    .container {
      padding: 0 30px;
    }

    .module-title {
      font-size: 40px;
      line-height: 50px;
    }
  }
}

@import './index/announcement.less';
@import './index/documents.less';
@import './index/extension.less';
@import './index/extension-btn.less';
@import './index/faqs.less';
@import './index/email-us.less';
