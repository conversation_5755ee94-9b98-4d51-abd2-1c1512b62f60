.chatdoc-edu-offer-page {
  font-weight: 400;
  font-family: Pop<PERSON>s;
  background: #fbfbfb;

  --toastify-toast-width: auto;

  .Toastify__toast-container.Toastify__toast-container--top-center {
    left: 50%;
    width: var(--toastify-toast-width);
    min-width: 212px;
    margin: 12vh auto;
    transform: translateX(-50%);
  }

  .footer-wrapper {
    z-index: 100;
    margin-top: -96px;

    .footer-bg,
    .footer-bg-mini {
      display: none;
    }
  }

  @import './edu-offer/advantages.less';
  @import './edu-offer/banner-and-advantages.less';
  @import './edu-offer/university.less';
  @import './edu-offer/offer-details.less';
  @import './edu-offer/participate.less';
  @import './edu-offer/discount-code.less';
  @import './edu-offer/notes.less';
  @import './edu-offer/faqs.less';
}

.edu-offer-email-template-popover {
  box-sizing: border-box;
  max-width: 1200px;
  padding: 40px 30px;
  border: 1px solid #6576db;
  border-radius: 40px;
  box-shadow: 0 20px 38px 0 rgba(76, 101, 251, 20%);

  --bs-popover-arrow-border: #6576db;
  --bs-popover-zindex: 95;

  .edu-offer-email-template {
    .template {
      color: #4e4f51;
      font-weight: 500;
      font-size: 16px;
      font-style: normal;
      line-height: 30px;
      letter-spacing: 2px;
      white-space: pre-wrap;
    }

    .operations {
      display: flex;
      gap: 24px;
      justify-content: flex-end;
      margin-top: 12px;

      .operation-item {
        display: flex;
        gap: 4px;
        padding: 10px 24px;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.1s;

        &.send-email {
          color: #fff;
          background: #6576db;

          &:hover {
            background: rgba(101, 118, 219, 80%);
          }
        }

        &.copy-text {
          color: #6576db;
          background: #eaedff;

          &:hover {
            background: rgba(234, 237, 255, 80%);
          }
        }
      }
    }
  }
}

@media (max-width: 1600px) {
  .edu-offer-email-template-popover {
    max-width: 1000px;
    padding: 30px 20px;
    border-radius: 30px;

    .edu-offer-email-template {
      .template {
        line-height: 28px;
      }

      .operations {
        .operation-item {
          padding: 6px 18px;
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .chatdoc-edu-offer-page {
    .footer-wrapper {
      margin-top: -80px;
      padding-bottom: 0;
    }
  }

  .edu-offer-email-template-popover {
    max-width: calc(100vw - 10px);
    padding: 16px;
    border-radius: 16px;

    .edu-offer-email-template {
      .template {
        font-size: 12px;
        line-height: 20px;
      }

      .operations {
        .operation-item {
          svg {
            width: 20px;
            height: 20px;
          }

          font-size: 12px;
        }
      }
    }
  }
}
