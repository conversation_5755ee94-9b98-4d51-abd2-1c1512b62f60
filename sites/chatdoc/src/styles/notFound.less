:root {
  --page-height: 100vh;
}

.not-found-page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: var(--page-height);
  font-weight: 400;
  font-family: Poppins;

  &::after {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    opacity: 0.6;
    content: '';
  }

  .not-found-wrapper {
    position: relative;
    z-index: 1;
  }
}
