.explore-wrapper {
  padding: 191px 0;
  text-align: center;

  .explore-images-swiper {
    padding: 69px 0 135px;
  }

  .swiper-slide {
    background: linear-gradient(130deg, #000 1%, #222 114.3%);
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 16px;
  }

  .explore-item-box {
    display: flex;
    flex-direction: column;
    height: 450px;
    padding: 40px 0 61px;
  }

  .explore-image-box {
    flex: 1;

    .explore-image {
      width: 100%;
      height: auto;
    }
  }

  .explore-name {
    font-weight: 600;
    font-size: 24px;
    font-family: Poppins;
    line-height: 30px;
    white-space: pre;
    text-align: center;
    background: linear-gradient(
      88deg,
      #fff 2.84%,
      rgba(255, 255, 255, 80%) 76.02%
    );
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .swiper-button-prev,
  .swiper-button-next {
    top: auto;
    bottom: 0;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    padding: 18px;
    background: rgba(255, 255, 255, 5%);
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 100px;
    transform: translateX(-50%);

    &::after {
      color: #fff;
      font-size: 24px;
      user-select: none;
    }
  }

  .swiper-button-prev {
    left: calc(50% - 35px);
  }

  .swiper-button-next {
    left: calc(50% + 35px);
  }
}

@media (max-width: @small-screen-size) {
  .explore-wrapper {
    padding: 39px 0;

    .explore-images-swiper {
      padding: 55px 0 120px;
    }
  }
}
