.guide-menu {
  width: 320px;
  height: 100%;
  overflow-y: auto;
  background: #fff;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    width: 6px;
    height: 6px;
    background: transparent;

    &:horizontal {
      background: transparent;
    }

    border-radius: 3px;
    cursor: pointer;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: #cfcfd1;

    &:horizontal {
      background-color: #cfcfd1;
    }
  }

  &::-webkit-scrollbar-track {
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }

  .ant-menu {
    border-inline-end: none !important;
  }

  .ant-menu-item-icon {
    width: 24px;
    height: 24px;
  }

  .ant-menu-title-content {
    flex: auto;
    min-width: 0;
    overflow: hidden;
    color: #3d434e;
    font-weight: 400;
    font-family: Poppins;
    text-overflow: ellipsis;
    margin-inline-start: 8px;
  }

  .ant-menu-submenu {
    position: relative;

    &::after {
      position: absolute;
      right: 20px;
      bottom: 0;
      left: 24px;
      height: 1px;
      background: #eee;
      content: '';
    }
  }

  .ant-menu-item {
    display: flex;
    align-items: center;
    height: 56px;
    padding-left: 48px;
    font-weight: 400;
    line-height: 56px;
    white-space: nowrap;
    padding-inline: 16px;

    .guide-link {
      color: #3d434e;
    }
  }

  .ant-menu-submenu-arrow {
    color: #303133;
  }

  .ant-menu-submenu-title {
    display: flex;
    align-items: center;
    height: 56px;
    padding-left: 24px;
    font-weight: 500;
    line-height: 56px;
    white-space: nowrap;
    padding-inline: 16px;
    padding-inline-end: 36px;

    .guide-link {
      color: #142132;
    }

    &:hover {
      .guide-link {
        color: #5a65ea;
      }
    }
  }

  [guide-active='true'] {
    background: #f1f3ff;

    .guide-link {
      color: #5a65ea;
    }

    &:hover {
      background: #f1f3ff !important;
    }

    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 3px;
      background: #5a65ea;
      content: '';
    }
  }
}
