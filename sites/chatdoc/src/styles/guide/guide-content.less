.guide-content {
  display: flex;
  flex: 1;
  gap: 30px;

  .content {
    flex: 1;

    img,
    video {
      width: 100%;
      margin-bottom: 32px;
    }

    blockquote {
      padding: 0 16px;
      border-left: 8px solid #5a65ea;

      p {
        margin-bottom: 0;
        color: #303133;
        font-weight: 700;
        font-size: 18px;
        font-family: Poppins;
        line-height: 32px;
      }
    }

    table {
      display: block;
      margin-bottom: 10px;
      overflow-x: auto;

      thead {
        background-color: rgb(234, 234, 239);
      }

      tr {
        border: 1px solid rgb(220, 220, 228);
      }

      th {
        padding: 16px;
      }

      td {
        padding: 12px;
      }

      th,
      td {
        border-right: 1px solid rgb(220, 220, 228);
        border-left: 1px solid rgb(220, 220, 228);
      }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    strong,
    b {
      font-weight: 700;
      font-family: Poppin;
    }

    h1 {
      margin-bottom: 54px;
      color: #000;
      font-size: 42px;
      line-height: 45px;
    }

    h2 {
      color: #000;
      font-size: 32px;
      line-height: 45px;
    }

    h3 {
      color: #303133;
      font-size: 28px;
      line-height: 42px;
    }

    h4 {
      color: #303133;
      font-size: 24px;
      line-height: 24px;
    }

    h5 {
      color: #303133;
      font-size: 20px;
      line-height: 14px;
    }

    h6 {
      color: #303133;
      font-size: 18px;
      line-height: 14px;
    }

    p {
      color: #606266;
      font-weight: 400;
      font-size: 18px;
      line-height: 32px;

      > img,
      > video {
        margin-top: 32px;
      }
    }

    ul,
    ol {
      padding-inline-start: 40px;

      li {
        overflow-wrap: break-word;
      }
    }

    ul {
      list-style: initial;
    }

    ol {
      list-style: decimal;
    }

    blockquote,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    ul,
    ol {
      margin-bottom: 16px;
    }

    // :is(p, h1, h2, h3, h4, h5, h6, ul, ol)
    //   + :is(h1, h2, h3, h4, h5, h6, img, video) {
    //   margin-bottom: 32px;
    // }
    p + h1,
    p + h2,
    p + h3,
    p + h4,
    p + h5,
    p + h6,
    p + img,
    p + video,
    h1 + h1,
    h1 + h2,
    h1 + h3,
    h1 + h4,
    h1 + h5,
    h1 + h6,
    h1 + img,
    h1 + video,
    h2 + h1,
    h2 + h2,
    h2 + h3,
    h2 + h4,
    h2 + h5,
    h2 + h6,
    h2 + img,
    h2 + video,
    h3 + h1,
    h3 + h2,
    h3 + h3,
    h3 + h4,
    h3 + h5,
    h3 + h6,
    h3 + img,
    h3 + video,
    h4 + h1,
    h4 + h2,
    h4 + h3,
    h4 + h4,
    h4 + h5,
    h4 + h6,
    h4 + img,
    h4 + video,
    h5 + h1,
    h5 + h2,
    h5 + h3,
    h5 + h4,
    h5 + h5,
    h5 + h6,
    h5 + img,
    h5 + video,
    h6 + h1,
    h6 + h2,
    h6 + h3,
    h6 + h4,
    h6 + h5,
    h6 + h6,
    h6 + img,
    h6 + video,
    ul + h1,
    ul + h2,
    ul + h3,
    ul + h4,
    ul + h5,
    ul + h6,
    ul + img,
    ul + video,
    ol + h1,
    ol + h2,
    ol + h3,
    ol + h4,
    ol + h5,
    ol + h6,
    ol + img,
    ol + video {
      margin-top: 32px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .guide-content {
    .content {
      img,
      video {
        width: 100%;
        margin-bottom: 16px;
        pointer-events: none;
      }

      h1 {
        margin-bottom: 24px;
        color: #303133;
        font-size: 24px;
        line-height: 28px;
      }

      h2 {
        font-size: 20px;
        line-height: 24px;
      }

      h3 {
        font-size: 18px;
        line-height: 24px;
      }

      h4 {
        font-size: 17px;
        line-height: 20px;
      }

      h5 {
        font-size: 16px;
        line-height: 20px;
      }

      h6 {
        font-size: 15px;
        line-height: 20px;
      }

      p {
        font-size: 14px;
        line-height: 20px;

        > img,
        > video {
          margin-top: 16px;
        }
      }

      ul,
      ol {
        padding-inline-start: 24px;
      }

      blockquote,
      h2,
      h3,
      h4,
      h5,
      h6,
      p,
      ul,
      ol {
        margin-bottom: 8px;
      }

      p + h1,
      p + h2,
      p + h3,
      p + h4,
      p + h5,
      p + h6,
      p + img,
      p + video,
      h1 + h1,
      h1 + h2,
      h1 + h3,
      h1 + h4,
      h1 + h5,
      h1 + h6,
      h1 + img,
      h1 + video,
      h2 + h1,
      h2 + h2,
      h2 + h3,
      h2 + h4,
      h2 + h5,
      h2 + h6,
      h2 + img,
      h2 + video,
      h3 + h1,
      h3 + h2,
      h3 + h3,
      h3 + h4,
      h3 + h5,
      h3 + h6,
      h3 + img,
      h3 + video,
      h4 + h1,
      h4 + h2,
      h4 + h3,
      h4 + h4,
      h4 + h5,
      h4 + h6,
      h4 + img,
      h4 + video,
      h5 + h1,
      h5 + h2,
      h5 + h3,
      h5 + h4,
      h5 + h5,
      h5 + h6,
      h5 + img,
      h5 + video,
      h6 + h1,
      h6 + h2,
      h6 + h3,
      h6 + h4,
      h6 + h5,
      h6 + h6,
      h6 + img,
      h6 + video,
      ul + h1,
      ul + h2,
      ul + h3,
      ul + h4,
      ul + h5,
      ul + h6,
      ul + img,
      ul + video,
      ol + h1,
      ol + h2,
      ol + h3,
      ol + h4,
      ol + h5,
      ol + h6,
      ol + img,
      ol + video {
        margin-top: 16px;
      }
    }
  }
}
