.guide-anchor {
  position: sticky;
  top: 120px;
  z-index: 10;
  height: 100%;

  .name {
    margin-bottom: 20px;
    color: #142132;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
  }

  .ant-anchor {
    position: relative;

    &::before {
      position: absolute;
      inset-inline-start: 0;
      top: 0;
      height: 100%;
      border-inline-start: 2px solid rgba(5, 5, 5, 6%);
      content: ' ';
    }
  }

  .ant-anchor-link {
    padding-block: 4px;
    padding-inline: 16px 0;

    .ant-anchor-link {
      padding-block: 2px;
    }
  }

  .ant-anchor-link-title {
    position: relative;
    display: block;
    height: 22px;
    overflow: hidden;
    color: rgba(0, 0, 0, 88%);
    font-weight: 400;
    font-size: 14px;
    font-family: Poppins;
    white-space: nowrap;
    text-overflow: ellipsis;
    transition: all 0.3s;
    margin-block-end: 3px;

    &:only-child {
      margin-block-end: 0;
    }
  }

  .ant-anchor-link-title-active {
    color: #6576db !important;
  }
}
