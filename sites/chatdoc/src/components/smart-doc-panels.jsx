import React, { useEffect, useRef } from 'react';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import AutoSwitchCards from './auto-switch-cards';

import '../styles/smart-doc-panels.less';
import classnames from 'classnames';

const SmartDocPanels = () => {
  const { allSmartDocPanelsData } = useGlobalContext();
  const smartDocPanelsRef = useRef();
  const firstPanelRef = useRef();
  const secondPanelRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const isAnyVisible = entries.some((entry) => entry.isIntersecting);
        if (isAnyVisible) {
          setTimeout(() => {
            smartDocPanelsRef.current?.classList.add('active');
          }, 500);
        } else {
          smartDocPanelsRef.current?.classList.remove('active');
        }
      },
      {
        threshold: 0.5,
      },
    );

    if (firstPanelRef.current) {
      observer.observe(firstPanelRef.current);
    }
    if (secondPanelRef.current) {
      observer.observe(secondPanelRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <div className="panel-wrapper" ref={smartDocPanelsRef}>
      <Container className="panel-container">
        <div className="panel-row">
          {allSmartDocPanelsData.map((item, index) => (
            <div
              className="panel-item"
              key={index}
              ref={
                index === 0
                  ? firstPanelRef
                  : index === 1
                    ? secondPanelRef
                    : null
              }>
              {item.images ? (
                item.images.map((imageItem) => (
                  <GatsbyImage
                    key={imageItem.name}
                    image={imageItem.image.childImageSharp.gatsbyImageData}
                    alt=""
                    className={classnames({
                      'panel-image': true,
                      [`panel-image-${imageItem.name}`]: true,
                    })}
                  />
                ))
              ) : (
                <AutoSwitchCards itemList={item.imagesList} />
              )}
              <pre className="panel-desc">{item.desc}</pre>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default SmartDocPanels;
