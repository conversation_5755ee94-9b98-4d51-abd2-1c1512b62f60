export const BANNER_DATA = {
  title: 'Chat with documents.',
  subtitle: 'Get instant answers with cited sources.',
  desc: 'Dive into PDFs like never before with ChatDOC. Let AI summarize long documents, \nexplain complex concepts, and find key information in seconds.',
};

export const V3_BANNER_DATA = {
  title: 'Where',
  subtitle: 'Speaks Accurately',
  desc: 'Answers You Can Trust. Sources You Can See.',
};

export const BANNER_TITLE = [
  'PDF',
  'DOCX',
  'EPUB',
  'Scan',
  'Markdown',
  'Web',
  'TXT',
];

export const FOOTER_DATA = [
  {
    title: 'Product',
    links: [
      {
        name: 'ChatDOC',
        linkProduct: 'chatdoc',
        link: 'project',
      },
      {
        name: 'ChatPaper',
        linkProduct: 'chatpaper',
        link: 'project',
      },
      {
        name: 'PDF Parser',
        linkProduct: 'pdfparser',
        link: 'index',
      },
    ],
  },
  {
    title: 'Resources',
    links: [
      {
        name: 'Blog',
        linkProduct: 'chatdoc',
        link: 'blog',
      },
      {
        name: 'Help Center',
        linkProduct: 'chatdoc',
        link: 'help',
      },
      {
        name: 'API',
        linkProduct: 'chatdoc',
        link: 'api',
      },
    ],
  },
  {
    title: 'Company & Legal',
    links: [
      {
        name: 'About Us',
        linkProduct: 'chatdoc',
        link: 'about',
      },
      {
        name: 'Privacy Policy',
        linkProduct: 'chatdoc',
        link: 'policy',
      },
      {
        name: 'Terms of Service',
        linkProduct: 'chatdoc',
        link: 'terms',
      },
    ],
  },
];

export const EDU_BANNER_DATA = {
  title: 'An AI Reading Assistant to \nFuel Your Study & Work',
  desc: 'Dive into documents like never before with ChatDOC. Let AI summarize long \ndocuments, explain complex concepts, and find key information in seconds.',
};

export const EDU_OFFER_BANNER_DATA = {
  title:
    'ChatDOC Back-to-School \n <span class="special-offer">Special Offer</span>',
  desc: '📚 Ready to ace your semester? ChatDOC has an amazing offer for you!',
};

export const LINK_DATA = {
  title: 'Your PDF AI is Ready to Answer Questions!',
  desc: 'Join professionals, students and researchers to understand \nresearch and do high-quality work with ChatDOC.',
};

export const BLOG_DETAIL_TRY_CARD_DATA = {
  title: 'Read professional documents faster than ever.',
  desc: 'Get serious and accurate results with ChatDOC, \nyour professional-grade PDF Chat AI.',
};

export const BLOG_REG_GUIDE_MODAL_DATA = {
  url: { product: 'chatdoc', pathName: 'signUp' },
  title: 'ChatDOC - Chat with Any PDF',
  desc: 'Chat with various files to let AI search, analyze, explain and summarize key information intelligently in seconds.',
};

const FAQS_QUESTION1 = {
  question: 'Is ChatDOC free to use?',
  answer:
    'Yes, you can sign up for a free ChatDOC account. In the free plan, file size is now limited to 300 pages, and you can upload up to 10 docs. You can upgrade your plan to get more quota and pro features.',
  isDefaultShowAnswer: 'true',
};

const FAQS_QUESTION2 = {
  question: 'Where are my files stored?',
  answer:
    'We store your files in encrypted cloud storage to ensure their security. Our strict security protocols guarantee the utmost safety for your information and protect it from potential threats. You have full ownership and control of your data, and you can delete any files completely at any time.',
};

const FAQS_QUESTION3 = {
  question: 'Can I search across multiple documents?',
  answer:
    'Yes, of course! Upload a folder of files then chat with them at once. Get summarized answers from multiple files, and easily navigate through all files on the sidebar.',
};

const FAQS_QUESTION4 = {
  question: 'What new features are coming?',
  answer:
    'We are working on the following features, such as improved citations, more file types supported. Follow us on Twitter and stay up to date with our latest news!',
};

export const FAQS_DATA = [
  {
    position: 'left',
    faqs: [FAQS_QUESTION1, FAQS_QUESTION3],
  },
  {
    position: 'right',
    faqs: [FAQS_QUESTION2, FAQS_QUESTION4],
  },
];

export const FAQS_DATA_MOBILE = [
  FAQS_QUESTION1,
  FAQS_QUESTION2,
  FAQS_QUESTION3,
  FAQS_QUESTION4,
];

export const EDU_OFFER_DETAILS_DATA = [
  {
    title: 'Duration:',
    texts: [
      {
        text: 'Now until\n',
        fontWeight: 500,
        fontFamily: 'Poppins',
      },
      {
        text: 'December 31, 2024',
        fontWeight: 700,
        fontFamily: 'Poppins',
      },
    ],
  },
  {
    title: 'Discount:',
    texts: [
      {
        text: '20% off\n',
        fontWeight: 700,
        fontFamily: 'Poppins',
        color: '#FF7D1A',
        className: 'discount',
      },
      {
        text: 'on all orders',
        fontWeight: 500,
        fontFamily: 'Poppins',
      },
    ],
  },
  {
    title: 'Eligibility: ',
    texts: [
      {
        text: 'Current ',
        fontWeight: 500,
        fontFamily: 'Poppins',
      },
      {
        text: 'students\n',
        fontWeight: 700,
        fontFamily: 'Poppins',
      },
      {
        text: 'and ',
        fontWeight: 500,
        fontFamily: 'Poppins',
      },
      {
        text: 'teachers',
        fontWeight: 700,
        fontFamily: 'Poppins',
      },
    ],
  },
];

export const EDU_OFFER_EMAIL_SUBJECT = 'ChatDOC Student Discount Application';

export const EDU_OFFER_EMAIL_TEMPLATE = `Dear ChatDOC Team,

I am [Your Name], currently enrolled at [School Name]. I am very interested in ChatDOC and would like to apply for your Back-to-School special offer.

Here is my application information:

Name: [Your Name]
School: [School Name]
Status: [Student/Teacher]
Educational Email: [Your .edu email address]
ChatDOC email address ：[Your registered ChatDOC email address (if you already have an account)]

I have attached a photo of my student/teacher ID as proof of my status.

I look forward to receiving your response and the discount code. Thank you!

Best regards,
[Your Name]
`;

export const EDU_OFFER_PARTICIPATE_DATA = {
  title: 'How to \nParticipate',
  step1: '1. Send an application email to ',
  step2: `2. Email Subject: ${EDU_OFFER_EMAIL_SUBJECT}`,
  step3: '3. Please include in your email:',
  infos: [
    'Full Name',
    'School Name',
    'Proof of student/teacher status (photo of student/teacher ID)',
    'Valid educational email address (.edu emails preferred)',
    'Your registered ChatDOC email address (if you already have an account)',
  ],
};

export const EDU_OFFER_NOTES_DATA = [
  'Discount code can be used once per account',
  'The code is applicable for both monthly and annual plans, for both one-time purchases and recurring subscriptions',
  'For recurring subscriptions, the 20% discount applies only to the first billing period (first month for monthly plans, first year for annual plans). Subsequent renewals will be charged at the regular price.',
  'Code valid until December 31, 2024',
  'For any questions, please contact our customer support: ',
];

export const EDU_OFFER_FAQS_DATA = [
  {
    question:
      "I'm an educator or student who purchased a membership within the last month. Can I still participate in this offer?",
    answer:
      "Absolutely! If you're already a paid user, don't worry! Once your student/teacher status is verified, we'll gift you 30 GPT-4o questions package to enhance your ChatDOC experience.",
  },
  {
    question: "I'm a graduate student. Am I eligible for this offer?",
    answer:
      "Yes! As long as you're a current student (including undergraduate, graduate, or doctoral student) or a teacher, you're eligible for this offer.",
  },
  {
    question: 'If my application is rejected, can I apply again?',
    answer:
      'Yes, if your application is rejected, you can review the reason for rejection, provide additional or corrected information, and reapply.',
  },
  {
    question:
      'Is the discount code applicable to all types of ChatDOC subscriptions?',
    answer:
      'Yes, this discount applies to all types of ChatDOC subscriptions, including monthly and annual plans.',
  },
  {
    question: 'How does the discount work for recurring subscriptions?',
    answer:
      "For recurring subscriptions, the 20% discount is applied only to your first billing period. This means if you choose a monthly subscription, you'll get 20% off for the first month. If you choose an annual subscription, you'll get 20% off for the first year. After the initial discounted period, your subscription will be renewed at the regular price. This approach allows you to enjoy significant savings while trying out our premium features!",
  },
];
