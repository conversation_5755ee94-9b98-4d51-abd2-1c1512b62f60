.chatdb-index-page {
  font-weight: 400;
  font-family: Poppins;

  // TODO（未处理移动端适配的特殊处理）
  .container {
    min-width: 1300px;
  }

  @media (max-width: 1400px) {
    .container {
      max-width: 540px;
      margin: 0 100px;
    }

    .header-wrapper,
    .banner-wrapper,
    .join-waitlist-wrapper,
    .product-highlights-wrapper,
    .use-case-wrapper {
      width: fit-content;
    }

    footer {
      width: calc(1300px + 100px * 2);
    }
  }
}

@import './components/variable.less';
@import './components/common.less';
@import './components/join-waitlist-input.less';
@import './components/header.less';
@import './index/banner.less';
@import './index/product-highlights.less';
@import './index/use-case.less';
@import './index/join-waitlist.less';
@import './components/footer.less';
@import './index/media.less';
