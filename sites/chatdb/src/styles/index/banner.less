.banner-wrapper {
  position: relative;

  &::after {
    position: absolute;
    top: 0;
    z-index: -2;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    opacity: 0.6;
    content: '';
  }

  .banner-container {
    display: flex;
    justify-content: space-between;
  }

  .banner-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 52%;
  }

  .banner-title {
    width: fit-content;
    color: @dark-title-color;
    font-weight: 700;
    font-size: 40px;
    font-family: Poppins;
    line-height: 60px;
    white-space: nowrap;
  }

  .banner-title-highlight {
    position: relative;
    margin-left: 10px;
    padding: 7px;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(269deg, #84d1a8 0%, #6576db 100%);
      border-radius: @border-radius-base;
      opacity: 0.3;
      content: '';
    }
  }

  .banner-desc {
    width: fit-content;
    margin: 30px 0 60px;
    color: @dark-text-color;
    font-size: 18px;
    line-height: 36px;
    white-space: nowrap;
  }

  .banner-right {
    display: flex;
    justify-content: flex-end;
  }

  .banner-image {
    width: 85%;
    margin-top: 60px;
  }
}
