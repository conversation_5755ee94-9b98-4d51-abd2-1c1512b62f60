.product-highlights-wrapper {
  .product-highlights-item {
    padding: 80px 0;
    border-bottom: @border-base;

    &:nth-child(2n) {
      background-color: #fbfbfb;

      .highlight-row {
        flex-direction: row-reverse;
        justify-content: flex-end;
      }

      .highlight-right {
        flex: none;
        margin-right: 0;
        margin-left: -50px;
      }
    }
  }

  .highlight-row {
    align-items: center;
    justify-content: space-between;
  }

  .highlight-left {
    width: 41%;
  }

  .highlight-title {
    width: 60%;
    font-weight: 700;
    font-family: Poppins;
    line-height: 54px;
    text-align: left;
  }

  .highlight-subtitle {
    margin-bottom: 90px;
    text-align: left;
  }

  .highlight-multimodal-list {
    display: flex;
    column-gap: 10%;
  }

  .multimodal-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 64px;
  }

  .multimodal-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 10px;
  }

  .multimodal-name {
    color: @text-color;
    font-size: 18px;
    line-height: 36px;
    white-space: nowrap;
  }

  .highlight-right {
    display: flex;
    justify-content: flex-end;
    width: 55%;
    margin-right: -50px;
  }

  .highlight-image {
    width: 800px;
  }
}
