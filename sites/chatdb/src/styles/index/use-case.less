.use-case-wrapper {
  padding: 120px 0;
  border-bottom: 1px solid #dedede;

  .use-case-title {
    margin-bottom: 90px;
  }

  .use-case-content {
    display: flex;
    justify-content: center;
  }

  .input-title,
  .output-title {
    margin-bottom: 29px;
    color: @dark-title-color;
    font-weight: 500;
    font-size: 20px;
    font-family: Poppins;
    line-height: 22px;
  }

  .content-input,
  .content-output {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .input-item-question-box,
  .output-table-bg {
    &::after {
      position: absolute;
      bottom: -1px;
      left: -5px;
      z-index: -1;
      width: 20px;
      height: 14px;
      content: '';
    }
  }

  .input-item-question-box {
    &::after {
      background: url('../../assets/images/bubble-frame-tail-dark.svg');
    }
  }

  .output-table-bg {
    &::after {
      z-index: -2;
      background: url('../../assets/images/bubble-frame-tail-light.svg');
    }
  }

  .content-input {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 18px;
  }

  .input-title {
    position: relative;
    left: -15px;
  }

  .input-list {
    width: calc(var(--input-list-width) - var(--input-item-selected-left));
    border-right: 1px solid #dedede;
  }

  .input-item {
    margin-bottom: 10px;
    list-style-type: circle;

    &:last-child {
      margin-bottom: 0;
    }

    &::marker {
      color: #6576db;
    }
  }

  .input-item-active {
    .input-item-selected {
      left: calc(-1 * var(--input-item-selected-left));
      width: calc(100% + var(--input-item-selected-left));
    }

    .input-item-name {
      font-weight: 500;
      font-family: Poppins;
    }

    .input-item-question-box {
      display: block;
    }
  }

  .input-item-name-box {
    position: relative;
    cursor: pointer;
  }

  .input-item-name {
    padding: 9px 0 10px 8px;
    color: @dark-text-color;
    font-size: 18px;
    line-height: 36px;
  }

  .input-item-question-box {
    position: relative;
    left: -30px;
    display: none;
    width: calc(100% + var(--input-item-selected-left));
    margin: 10px 0 19px;
    background-image: linear-gradient(225deg, #9d9df9, #1d45c2);
    border-radius: 15px;
  }

  .input-item-question {
    padding: 30px;
    color: #fff;
  }

  .content-middle {
    display: flex;
    align-items: center;
    height: 500px;
  }

  .content-arrow {
    height: 28px;
    margin: 0 30px;
  }

  .file-type-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 310px;
    height: 292px;
    padding: 45px 76px;
    border: @border-base;
    border-radius: 40px;
  }

  .card-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
  }

  .card-title {
    margin-bottom: 30px;
    color: @dark-title-color;
    font-weight: 500;
    font-size: 20px;
    font-family: Poppins;
    line-height: 22px;
  }

  .card-desc {
    color: #9ea0a5;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 32px;
    white-space: pre;
    text-align: center;
  }

  .output-table-box {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .output-table-bg {
    position: absolute;
    bottom: var(--output-table-bg-bottom);
    left: var(--output-table-bg-left);
    z-index: -1;
    width: calc(100% - (var(--output-table-bg-left) * 2));
    height: calc(100% - var(--output-table-bg-bottom));
    background-color: rgba(101, 118, 219, 10%);
    border-radius: 15px;
  }

  .output-desc {
    padding: 20px 40px 0;
    color: #6576db;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.408px;
  }
}
