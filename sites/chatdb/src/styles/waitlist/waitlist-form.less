.waitlist-form {
  width: 80%;
  max-width: 960px;
  margin: 0 auto;
  padding: 0 60px 60px;
  background-color: #fff;
  border: @border-base;
  border-radius: @border-radius-base;
  box-shadow: 0 20px 38px rgba(101, 122, 147, 10%);

  .form-group {
    position: relative;
    margin-top: 30px;
    font-size: 14px;
    line-height: 22px;
  }

  .form-label {
    margin-bottom: 10px;
    color: @dark-color;
    font-size: inherit;
    line-height: inherit;
  }

  .form-label-required {
    &::after {
      color: #e6492d;
      font-size: 15px;
      content: '*';
    }
  }

  .form-control {
    height: 38px;
    padding: 8px 12px;

    &.is-valid,
    &.is-invalid {
      padding-right: 40px;
      background-image: none !important;
    }
  }

  .form-select {
    padding: 0 !important;
    background-image: none !important;

    .form-select__control {
      min-height: 36px;
      border: none;
      box-shadow: none;
    }

    .form-select__value-container {
      padding: 0 0 0 12px;
    }

    .form-select__placeholder {
      margin: 0;
      overflow: hidden;
      color: #9ea0a5;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .form-select__input-container {
      margin: 0;
    }

    .form-select__indicator-separator {
      display: none;
    }

    & ~ .invalid-feedback {
      &::after {
        right: 32px;
      }
    }

    &.is-valid,
    &.is-invalid {
      .form-select__value-container {
        padding-right: 12px;
      }
    }
  }

  .form-control,
  .form-select {
    width: 390px;
    color: #6b6c6f;
    font-size: inherit;
    line-height: inherit;
    background-color: #fff;
    border: 1px solid #e2e5ed;
    border-radius: @border-radius-base;
    box-shadow: inset 0 1px 2px rgba(102, 113, 123, 21%);

    &::placeholder {
      color: #9ea0a5;
    }

    &.is-valid,
    &.is-invalid {
      & ~ .invalid-feedback {
        display: block;

        &::after {
          display: block;
        }
      }
    }

    &.is-valid {
      border-color: #198754;

      & ~ .invalid-feedback {
        &::after {
          background-image: url('../../../../../common/assets/images/input-right.svg');
        }
      }
    }

    &.is-invalid {
      border-color: #e6492d;

      & ~ .invalid-feedback {
        &::after {
          background-image: url('../../../../../common/assets/images/input-error.svg');
        }
      }
    }

    & ~ .invalid-feedback {
      width: 390px;
    }
  }

  .form-textarea {
    width: 100%;
    height: 152px;
    resize: none;
  }

  .form-textarea-count {
    position: absolute;
    right: 15px;
    bottom: 1px;
    padding: 0 3px;
    color: #9ea0a5;
    font-size: inherit;
    line-height: inherit;
    background-color: #fff;
  }

  .form-group-captcha {
    .input-group-captcha {
      flex-wrap: nowrap;
    }

    .form-control-box {
      width: 390px;
    }

    .form-control-captcha {
      width: 100% !important;
      border-radius: @border-radius-base !important;
    }

    .send-captcha {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .captcha-img {
      margin: 0 10px;
    }

    .error-message {
      color: #e6492d;
      white-space: nowrap;
    }

    .refresh-btn {
      color: #6576db;
      line-height: 15px;
      border-bottom: 1px solid #6576db;
    }
  }

  .invalid-feedback {
    position: relative;
    color: #e6492d;
    font-size: 14px;
    line-height: 21px;

    p {
      position: absolute;
      white-space: nowrap;
    }

    &::after {
      position: absolute;
      top: -30px;
      right: 20px;
      display: none;
      width: 20px;
      height: 20px;
      background-repeat: no-repeat;
      background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
      content: '';
    }
  }

  .form-prompt {
    display: inline-block;
    margin-top: 60px;
    color: #6b6c6f;
    font-size: 14px;
    line-height: 22px;
  }

  .submit-btn {
    width: 120px;
    height: 41px;
    margin-top: 20px;
    padding: unset;
    color: #fff;
    font-weight: 500;
    font-size: 14px;
    font-family: Poppins;
    line-height: 21px;
    text-align: center;
    background-color: #6576db;
    border-color: #6576db;
  }
}
