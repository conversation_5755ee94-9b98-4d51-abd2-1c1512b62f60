.feature-wrapper {
  padding-top: 108px;

  .feature-item {
    padding: 120px 0;
    border-bottom: @border-base;

    &:nth-child(2n) {
      background-color: #fbfbfb;

      .feature-row {
        flex-direction: row-reverse;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .container {
    padding: 0;
  }

  .feature-row {
    flex-direction: row;
    justify-content: space-between;
    margin: 0;
  }

  .feature-content {
    max-width: 480px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 24px;
  }

  .text-title {
    margin-bottom: 58px;
    color: @dark-title-color;
    font-weight: 600;
    font-size: 36px;
    font-family: Poppins;
    line-height: 40px;
    letter-spacing: 2px;
    white-space: nowrap;
  }

  .text-desc {
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
  }

  .list-desc {
    ol {
      padding-left: 18px;
    }

    li {
      list-style-type: disc;

      &::marker {
        font-size: 16px;
      }
    }
  }

  .feature-video {
    width: 63%;
    padding: 0;
    border: @border-base;
  }
}
