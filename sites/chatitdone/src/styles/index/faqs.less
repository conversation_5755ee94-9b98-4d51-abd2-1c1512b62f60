.faqs-wrapper {
  position: relative;
  width: 100%;
  padding: 90px 0 60px;
  background: url('../../assets/images/footer-bg.png') bottom no-repeat;
  background-size: 90%;

  &::after {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    content: '';
  }

  .faqs-container {
    padding: 0;
  }

  .faqs-title {
    margin-bottom: 60px;
    color: @dark-title-color;
    font-weight: 600;
    font-size: 36px;
    line-height: 54px;
    letter-spacing: 2px;
    text-align: center;
  }

  .faqs-cards {
    width: 100%;
  }

  .faqs-cards-mobile {
    display: none;
  }

  .faqs-cards-container {
    position: absolute;
    display: flex;
    column-gap: 40px;
  }

  .faqs-cards-right,
  .faqs-cards-left {
    display: flex;
    flex: 1 1;
    flex-direction: column;
    row-gap: 40px;
  }

  .faqs-card {
    background-color: #fff;
    border: @border-base;
    border-radius: 40px;
    box-shadow: 0 20px 38px rgba(101, 122, 147, 10%);

    &.show {
      padding-bottom: 30px;
    }

    &:hover {
      background-color: #f5f5f5;

      .faqs-answer {
        background-color: #f5f5f5;
      }
    }
  }

  .faqs-question {
    cursor: pointer;

    &::after {
      content: unset;
    }

    &[aria-expanded='true'] {
      .question-text {
        padding-bottom: 20px;

        &::before {
          vertical-align: unset !important;
          transform: rotate(-90deg);
        }
      }
    }
  }

  .question-text {
    padding: 30px;
    color: @dark-title-color;
    font-weight: 700;
    font-size: 24px;
    font-family: Poppins;
    line-height: 42px;

    &::before {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 20px;
      vertical-align: middle;
      background: url('../../assets/images/arrow-down.svg') no-repeat;
      background-size: contain;
      content: '';
    }
  }

  .faqs-answer {
    position: relative !important;
    z-index: 1;
    width: 100%;
    padding: 0 30px;
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
    border: none;
    transform: none !important;
  }

  .faqs-footer {
    margin-top: 740px;
    text-align: center;
  }

  .footer-link {
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
    text-decoration: underline;
    text-underline-offset: 5px;
  }

  .footer-point {
    padding: 0 2px;

    &:last-child {
      display: none;
    }
  }
}
