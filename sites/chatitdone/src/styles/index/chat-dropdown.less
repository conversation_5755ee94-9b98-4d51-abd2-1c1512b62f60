.chat-nav-dropdown {
  position: relative;
  margin-right: auto;
  border-left: 1px solid #eaedf3;

  .chat-dropdown {
    margin-left: 12px;

    &:hover .dropdown-menu {
      display: block;
    }
  }

  .dropdown-toggle {
    position: relative;
    padding: 0 !important;

    &::after {
      content: unset;
    }

    &[aria-expanded='true'] {
      .menu-icon-bg {
        background-color: #6576db;
        opacity: 0.1;
      }
    }
  }

  .dropdown-menu {
    position: absolute;
    left: 0;
    margin: 0;
    padding-bottom: 0;
    border: none;
  }

  .menu-icon-bg {
    width: 40px;
    height: 40px;
    border-radius: @border-radius-base;

    &:hover {
      background-color: #6576db;
      opacity: 0.1;
    }
  }

  .menu-toggle-icon {
    position: absolute;
    top: 8px;
    left: 8px;
  }

  .chat-menu {
    padding: 30px 60px 60px;
    font-weight: 500;
    font-family: Poppins;
    border: @border-base;
    border-radius: @border-radius-base;
  }

  .menu-title {
    margin-bottom: 28px;
    color: #9ea0a5;
    font-size: 18px;
    line-height: 27px;
  }

  .menu-content {
    display: flex;
    flex-direction: column;
    row-gap: 30px;
  }

  .menu-item {
    display: flex;
  }

  .menu-icon {
    width: 40px;
    height: 40px;

    > div {
      width: 40px;
    }
  }

  .menu-text {
    margin-left: 17px;
    color: @text-color;
    white-space: nowrap;
  }

  .menu-text-active {
    color: #6576db;
  }

  .menu-name {
    font-size: 18px;
    line-height: 26px;
  }

  .menu-desc {
    font-weight: 500;
    font-size: 14px;
    line-height: 26px;
  }

  .navbar-dropdown {
    display: none;
  }
}
