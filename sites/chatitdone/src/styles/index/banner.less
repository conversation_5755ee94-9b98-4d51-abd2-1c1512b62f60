.banner-wrapper {
  position: relative;
  width: 100%;
  background: url('../../assets/images/banner-bg.png') top no-repeat;
  background-size: 90%;

  &::after {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    content: '';
  }

  .container {
    position: relative;
    top: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 75px;
  }

  .chatitdone-product-hunt {
    display: none;
  }

  .banner-title {
    margin-bottom: 30px;
    color: @dark-title-color;
    font-weight: 700;
    font-size: 50px;
    font-family: Poppins;
    line-height: 60px;
    text-align: center;
  }

  .banner-subtitle {
    margin-bottom: 20px;
    color: @dark-text-color;
    font-size: 18px;
    line-height: 27px;
    text-align: center;
  }

  .banner-btn {
    margin-bottom: 50px;
    padding: 10px 30px;
    color: #6576db;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    background-color: rgba(101, 118, 219, 10%);
    border: 1px solid #6576db;
    border-radius: @border-radius-base;
    backdrop-filter: blur(1.59119px);
  }

  .banner-image {
    width: 100%;
    max-width: 1125px;
    overflow: visible;
  }
}
