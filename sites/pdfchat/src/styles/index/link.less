.link-wrapper {
  padding-top: 0;

  .link-content {
    position: relative;
    top: -30px;
    z-index: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 80px;
    padding: 60px;
    background-color: #fff;
    border: @border-base;
    border-radius: 40px;
    box-shadow: 0 20px 38px 0 rgba(101, 122, 147, 10%);
  }

  .link-title {
    color: @dark-color;
    font-weight: 500;
    font-size: 40px;
    font-family: Poppins;
    line-height: 60px;
    letter-spacing: unset;
  }

  .link-subtitle {
    color: @text-color;
    font-size: 22px;
    line-height: 33px;
  }

  .link-title,
  .link-subtitle {
    text-align: left;
  }

  .link-title,
  .link-subtitle,
  .link-btn {
    margin-bottom: 0;
  }
}

@media (max-width: @middle-screen-size) {
  .link-wrapper {
    .link-content {
      padding: 40px;
    }

    .link-title {
      margin-bottom: 10px;
      font-size: 32px;
      line-height: 50px;
    }

    .link-subtitle {
      font-size: 18px;
      line-height: 26px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .link-wrapper {
    .link-content {
      top: -20px;
      padding: 30px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .link-wrapper {
    padding-top: 30px;

    .link-content {
      position: unset;
      flex-direction: column;
      margin-bottom: 60px;
      padding: 0;
      text-align: center;
      background-color: unset;
      border: none;
      box-shadow: unset;
    }

    .link-title {
      font-size: 24px;
      line-height: 36px;
    }

    .link-subtitle {
      margin-bottom: 30px;
      font-size: 14px;
      line-height: 36px;
    }

    .link-title,
    .link-subtitle {
      text-align: center;
    }
  }
}
