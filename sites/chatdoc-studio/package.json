{"name": "chatdoc-studio", "version": "0.1.0", "scripts": {"develop": "gatsby clean && cross-env SITE_ENV=test gatsby develop", "build": "gatsby clean && cross-env SITE_ENV=prod gatsby build", "build-test": "gatsby clean && cross-env SITE_ENV=test gatsby build", "build-staging": "gatsby clean && cross-env SITE_ENV=staging gatsby build", "build-cdn": "gatsby clean && cross-env SITE_ENV=prod gatsby build --prefix-paths", "build-cdn-test": "gatsby clean && cross-env SITE_ENV=test gatsby build --prefix-paths", "build-cdn-staging": "gatsby clean && cross-env SITE_ENV=staging gatsby build --prefix-paths", "serve": "gatsby serve", "clean": "gatsby clean", "lint": "eslint --fix --ext .js,.jsx src/"}, "dependencies": {"@uidotdev/usehooks": "^2.4.1", "antd": "^5.11.5", "gatsby": "^5.8.0", "gatsby-plugin-antd": "^2.2.0", "markdown-it": "^13.0.2", "markdown-it-anchor": "^8.6.7", "react-swipeable-views": "^0.14.0"}, "devDependencies": {"get-media-dimensions": "^2.0.2"}}