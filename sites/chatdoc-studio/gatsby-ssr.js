import React from 'react';
import GlobalProvider from './src/components/global-provider';
import { MobilePageContext } from '../../common/hooks/useMobilePageContext';

export const wrapPageElement = ({ element, props }) => {
  const { isMobilePage } = props.pageContext;

  return (
    <GlobalProvider>
      <MobilePageContext.Provider
        value={{ isMobilePage: Boolean(isMobilePage) }}>
        {element}
      </MobilePageContext.Provider>
    </GlobalProvider>
  );
};
