const fs = require('fs');
const path = require('path');

exports.onPreExtractQueries = (context) => {
  const { store } = context;
  const { program } = store.getState();

  fs.cpSync(
    path.resolve(__dirname, '../../common/hooks'),
    path.resolve(program.directory, '.cache/fragments/'),
    { recursive: true },
  );

  fs.cpSync(
    path.resolve(__dirname, '../../common/components'),
    path.resolve(program.directory, '.cache/fragments/'),
    { recursive: true },
  );
};

const createContextPages = (createPage, path, componentPath, context) => {
  createPage({
    path: path,
    component: componentPath,
    context: {
      ...context,
    },
  });
};

const createMobilePages = (createPage, path, componentPath, context) => {
  createPage({
    path: path.replace(/\/+$/, '') + '/m',
    component: componentPath,
    context: {
      ...context,
      isMobilePage: true,
    },
  });
};

exports.onCreatePage = ({ page, actions }) => {
  const { createPage } = actions;

  createContextPages(createPage, page.path, page.component);
  createMobilePages(createPage, page.path, page.component);
};
