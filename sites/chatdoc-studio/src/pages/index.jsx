import React from 'react';
import ChatDOCStudioSEO from '../components/seo';
import Layout from '../components/layout';
import Banner from '../components/banner';
import Ability from '../components/ability';
import Engine from '../components/engine';
import Agent from '../components/agent';
import Support from '../components/support';
import GitInfo from '../../../../common/components/gitInfo/git-info';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import '../styles/index.less';

export const Head = () => <ChatDOCStudioSEO />;

const IndexPage = () => {
  const { gitCommitData } = useGlobalContext();

  return (
    <>
      <Layout className="chatdoc-studio-index-page" isIndexPage hideFooter>
        <main className="chatdoc-studio-page-main">
          <Banner />
          <Ability />
          <Engine />
          <Agent />
          <Support />
        </main>
      </Layout>
      <GitInfo data={gitCommitData} />
    </>
  );
};

export default IndexPage;
