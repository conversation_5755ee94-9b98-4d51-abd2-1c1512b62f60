.header-wrapper {
  position: sticky;
  top: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  height: var(--header-height);
  background-color: color-mix(in oklab, oklch(10000% 0 0deg) 65%, transparent);
  box-shadow: none !important;
  backdrop-filter: blur(100px);

  &.shadow {
    // box-shadow:
    //   0 -1px #fff,
    //   0 0.5rem 1rem rgba(0, 0, 0, 15%) !important;
    border-bottom: 1px solid #e9ebf0;
  }

  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .logo-wrapper {
      .logo {
        width: 40px;
        height: 40px;
      }

      .name {
        margin-left: 10px;
        font-weight: 500;
        font-size: 20px;
        font-family: Poppins;
        line-height: 40px;
      }
    }

    .project-button {
      padding: 13px 16px;
      font-size: 16px;
    }
  }
}
