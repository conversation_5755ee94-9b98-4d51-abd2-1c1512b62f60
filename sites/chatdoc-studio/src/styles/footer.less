.footer-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 688px;
  padding-top: 140px;
  overflow: hidden;

  .content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .desc {
      font-weight: 500;
      font-size: 54px;
      line-height: 70px;
    }

    .project-button {
      margin-top: 30px;
    }
  }

  .bg {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 100%;
    transform: translate(-50%, -50%);

    .bg-inner {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      width: 100%;
      height: 100%;

      &::after {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 500px;
        background: linear-gradient(180deg, transparent, #fff);
        content: '';
      }

      &::before {
        position: absolute;
        top: 0;
        width: 100%;
        height: 500px;
        background: linear-gradient(180deg, #f1f3fa, transparent);
        content: '';
      }
    }

    &::before,
    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 1;
      width: 500%;
      height: 1000%;
      background: linear-gradient(
          180deg,
          #c1d2ed 1px,
          #c1d2ed 1px,
          transparent 1px
        )
        repeat;
      background-size: 100% 46px;
      transform: translate(-50%, -50%) rotate(30deg);
      content: '';
    }

    &::after {
      transform: translate(-50%, -50%) rotate(-30deg);
    }
  }

  .legal {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 20px;
    margin-top: 180px;

    .legal-item {
      color: #2d2d2d;
      text-decoration: underline;
      text-underline-offset: 4px;

      &:hover {
        color: #5971ed;
      }
    }
  }
}
