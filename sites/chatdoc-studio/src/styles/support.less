.support-wrapper {
  .support-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .support-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 70px;
      padding-bottom: 140px;

      .support-item {
        padding: 60px 60px 43px;
        background: #fbfbfc;
        border: 1px solid #e9ebf0;
        border-radius: 40px;

        .support-title {
          margin: 4px 0 6px;
          color: #5971ed;
          font-weight: 500;
          font-size: 24px;
          font-family: Poppins;
        }

        .support-desc {
          color: #505050;
          font-weight: 400;
          font-size: 18px;
          font-family: Poppins;
        }
      }
    }
  }
}
