.ability-wrapper {
  .ability-container {
    .ability-content {
      .ability-item {
        position: relative;
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 40px;
        overflow: hidden;
        background: #f1f3fa;
        border-radius: 40px;

        &:nth-child(odd) {
          flex-direction: row-reverse;

          .corner-image {
            right: 0;
            bottom: 0;
            left: unset;
            transform: rotate(270deg);
          }
        }

        .corner-image {
          position: absolute;
          bottom: 0;
          left: 0;
        }

        .image-side {
          width: 50%;
          max-width: 710px;
        }

        .main {
          display: flex;
          flex-direction: column;
          gap: 20px;
          align-items: center;
          justify-content: center;
          width: 50%;
          max-width: 710px;

          .main-content {
            width: max-content;

            .item-title {
              color: #363636;
              font-weight: 500;
              font-size: 34px;
              font-family: Poppins;
              white-space: break-spaces;
            }

            .item-desc {
              width: 430px;
              color: #8590a1;
              font-weight: 400;
              font-size: 18px;
              font-family: Poppins;
            }
          }
        }
      }
    }
  }
}
