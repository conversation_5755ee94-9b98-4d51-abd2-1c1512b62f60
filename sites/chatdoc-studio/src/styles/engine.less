.engine-wrapper {
  position: relative;

  .engine-container {
    position: relative;
    z-index: 10;

    .engine-content {
      .engine-process {
        padding-bottom: 60px;

        .engine-process-chart {
          display: flex;
          align-items: center;
          justify-content: center;

          .engine-process-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .engine-process-item-label {
              color: var(--, #363636);
              font-weight: 400;
              font-size: 18px;
              font-family: Poppins;
            }

            .engine-process-item-image {
              width: 270px;
            }
          }
        }
      }

      .engine-parser {
        position: relative;
        padding-bottom: 56px;
      }
    }
  }
}
