.chatdoc-studio-index-page {
  font-weight: 400;
  font-family: Pop<PERSON><PERSON>;
  background-color: #fff;

  // background-image: url('../assets/images/page-bg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;

  .chatdoc-studio-page-main {
    overflow: hidden;
  }

  .banner-wrapper {
    .banner-button {
      margin-bottom: 0;
    }

    @media (max-width: @mini-screen-size) {
      .banner-product-hunt {
        top: 13px;
      }
    }
  }

  .faqs-footer-wrapper {
    background-image: linear-gradient(180deg, #f6faff, #eaf3ff);
  }

  .module-title {
    font-weight: 600;
    font-size: 68px;
    font-family: poppins-blod;
    line-height: 70px;
    background: linear-gradient(
      88deg,
      #fff 2.84%,
      rgba(255, 255, 255, 80%) 76.02%
    );
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

@media (max-width: @mini-screen-size) {
  .chatdoc-index-page {
    .container {
      padding: 0 30px;
    }

    .module-title {
      font-size: 40px;
      line-height: 50px;
    }
  }
}
