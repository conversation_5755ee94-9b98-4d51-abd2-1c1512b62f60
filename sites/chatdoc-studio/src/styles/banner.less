.banner-wrapper {
  height: 1150px;

  .banner-container {
    position: relative;
    height: 100%;

    .banner-desc {
      padding-top: 128px;

      .banner-desc-sub {
        margin-bottom: 10px;
        color: var(--icon, #8590a1);
        font-weight: 500;
        font-size: 32px;
        font-family: Poppins;
      }

      .banner-desc-main {
        color: #363636;
        font-weight: 500;
        font-size: 74px;
        font-family: Poppins;
        line-height: 90px;

        .banner-desc-main-linear {
          position: relative;
          color: transparent;
          background: linear-gradient(to right, #1d45c2, #9d9df9);
          background-clip: text;

          .banner-desc-linear {
            position: absolute;
            bottom: -4px;
            left: 2px;
            width: calc(100% + 5px);
          }

          .banner-desc-file-icons {
            position: absolute;
            right: -170px;
            bottom: -20px;
            display: flex;
            gap: 7px;
            line-height: 32px;

            &::before {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                0deg,
                #fff 0%,
                rgba(255, 255, 255, 0%) 80%
              );
              content: '';
            }
          }
        }

        .banner-desc-main-last-row {
          position: relative;
          z-index: 2;
        }
      }

      .banner-desc-detail {
        width: 600px;
        margin: 10px 0 36px;
        color: #505050;
        font-weight: 400;
        font-size: 18px;
        font-family: Poppins;
      }
    }

    .banner-images {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;

      .bg {
        position: absolute;
        top: 431px;
        right: 20px;
        width: 960px;
      }

      .left {
        position: absolute;
        top: 545px;
        right: 496px;
        width: 557px;
      }

      .right1 {
        position: absolute;
        top: 250px;
        right: 17px;
        width: 479px;
      }

      .right2 {
        position: absolute;
        top: 786px;
        right: -40px;
        width: 536px;
      }
    }
  }
}
