.feature-box {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-content {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 20px;
  background: #f1f3fa;
  border-radius: 40px;
}

.feature-nav {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  width: fit-content;
  margin: 0 auto 64px;
  overflow: hidden;
  border-radius: 99px;
}

.active-bg {
  position: absolute;
  left: 0;
  z-index: 2;
  height: 100%;
  background: linear-gradient(135deg, #5bc7ef1a 0%, #4165f61a 100%);
  border-radius: 30px;
  transition: left 0.5s ease;
}

.feature-item {
  z-index: 3;
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  min-width: 188px;
  padding: 15px 30px;
  color: #6576db;
  font-weight: 500;
  font-size: 18px;
  line-height: 20px;
  cursor: pointer;

  &.active {
    color: transparent;
    background: linear-gradient(135deg, #5bc7ef 0%, #4165f6 100%);
    background-clip: text;
  }
}

.feature-tips {
  margin-top: 40px;
  margin-bottom: 40px;
  color: #3d434e;
  font-size: 24px;
  text-align: center;
}

.feature-content-main {
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  width: 100%;
  height: 740px;
  max-height: 740px;
  overflow: auto hidden;
  border: @border-base;
  border: 2px solid #dedede;
  border-radius: 6px;

  .iframe {
    width: 100%;
    min-width: 992px;
    height: 100%;
  }
}

.feature-content-main-mobile {
  .link-input-group {
    border-radius: @border-radius-base;
  }

  .link-input {
    padding: 7px 10px 7px 20px;
    overflow: hidden;
    color: #9ea0a5;
    line-height: 24px;
    text-overflow: ellipsis;
    border: 1px solid #e2e5ed;
    box-shadow: inset 0 1px 2px rgba(102, 113, 123, 21%);

    &::selection {
      text-overflow: unset;
    }
  }

  .link-btn {
    background-color: #6576db;
    border-color: #6576db;
    box-shadow:
      0 1px 1px rgba(22, 29, 37, 10%),
      inset 0 2px 0 rgba(255, 255, 255, 6%);
  }

  .link-desc {
    margin-top: 24px;
    color: #707070;
    font-size: 16px;
    line-height: 32px;
  }
}

.parser-bg-image {
  position: absolute !important;
  right: 0;
  bottom: -45%;
  left: -10%;
  width: 150%;
  height: 150%;
}

@media (max-width: @large-screen-size) {
  .preview-wrapper {
    .feature-content-main {
      height: 74vh;
    }

    .feature-nav {
      margin-bottom: 44px;
    }

    .feature-tips {
      margin-top: 15px;
      margin-bottom: 15px;
      font-size: 18px;
    }

    .upload-icon {
      width: 20px;
      height: 20px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .preview-wrapper {
    .feature-nav,
    .active-bg {
      border-radius: 12px;
    }

    .feature-item {
      padding: 12px 20px;
      font-size: 16px;
    }

    .feature-content-main {
      height: 60vh;
    }
  }
}

@media (max-width: @small-screen-size) {
  .preview-wrapper {
    .feature-nav {
      flex-wrap: nowrap;
      border-radius: 10px;
    }

    .active-bg {
      border-radius: 10px;
    }

    .feature-item {
      padding: 8px 16px;
      font-size: 14px;
    }

    .upload-icon {
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .preview-wrapper {
    .feature-content {
      width: 100%;
    }

    .feature-item {
      padding: 6px 10px;
      font-size: 12px;
    }

    .upload-icon {
      width: 14px;
      height: 14px;
    }

    .feature-tips {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 14px;
    }

    .feature-nav {
      margin-bottom: 24px;
    }
  }
}
