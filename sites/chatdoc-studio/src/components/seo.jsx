import React from 'react';
import PropTypes from 'prop-types';
import SEO from '../../../../common/components/seo/seo';

const DEFAULT_KEYWORDS = ['ChatDOC Studio'];

const ChatDOCStudioSEO = ({ title, description, keywords }) => {
  const META_LIST = [
    {
      name: 'keywords',
      content: keywords || DEFAULT_KEYWORDS,
    },
    {
      name: 'viewport',
      content:
        'width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no',
    },
  ];

  return (
    <SEO
      title={title || 'ChatDOC Studio'}
      description={
        description ||
        'Enterprise-grade Al assistants that deliver highly accurate answers from most complex documents, with 100% traceable insights.'
      }
      meta={META_LIST}
    />
  );
};

ChatDOCStudioSEO.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  keywords: PropTypes.array,
};

export default ChatDOCStudioSEO;
