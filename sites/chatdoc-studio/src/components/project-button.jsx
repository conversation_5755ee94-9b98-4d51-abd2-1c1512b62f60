import React from 'react';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import { getUrlWithProduct } from '../../../../common/urls';
import '../styles/project-button.less';

const ProjectButton = ({}) => {
  const { linkButtonText } = useGlobalContext();
  const projectHref = getUrlWithProduct('chatdoc-studio', 'project');

  return (
    <a
      href={projectHref}
      target="_blank"
      rel="noreferrer"
      className="project-button">
      {linkButtonText}
    </a>
  );
};

ProjectButton.propTypes = {};

export default ProjectButton;
