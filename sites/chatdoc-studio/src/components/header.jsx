import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Container } from 'react-bootstrap';
import classnames from 'classnames';
import { getEnvVariables } from '../../../../common/utils/env';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import { useHandleResize } from '../../../../common/hooks/useHandleResizeHook';
import '../styles/header.less';
import ProjectButton from './project-button';

const Header = ({}) => {
  const [isShowShadow, setIsShowShadow] = useState(false);

  const handleScroll = (e) => {
    const scrollTop = e.srcElement.scrollingElement.scrollTop;
    setIsShowShadow(!!scrollTop);
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const { currentProduct } = getEnvVariables();
  const { logo } = useGlobalContext();
  const [suggestBrowserTipHeight, setSuggestBrowserTipHeight] = useState(0);

  const getAnnouncementHeight = useCallback(() => {
    const suggestBrowserTipDom = document.querySelector('.suggest-browser-tip');
    if (suggestBrowserTipDom) {
      setSuggestBrowserTipHeight(suggestBrowserTipDom.clientHeight);
    }
  }, []);

  useHandleResize(() => {
    getAnnouncementHeight();
  });

  return (
    <header
      className={classnames({
        'header-wrapper': true,
        shadow: isShowShadow,
      })}
      style={{ marginTop: `${suggestBrowserTipHeight}px` }}>
      <Container>
        <span className="logo-wrapper">
          <img src={logo} alt={`${currentProduct} Logo`} className="logo" />
          <span className="name">{currentProduct}</span>
        </span>
        <ProjectButton />
      </Container>
    </header>
  );
};

Header.propTypes = {};

export default Header;
