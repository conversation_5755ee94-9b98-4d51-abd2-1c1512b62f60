import React from 'react';
import { Container } from 'react-bootstrap';
import '../styles/ability.less';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import { GatsbyImage } from 'gatsby-plugin-image';
import PanelTitle from './panel-title';

const Ability = ({}) => {
  const { abilityData } = useGlobalContext();

  return (
    <div className="ability-wrapper">
      <Container className="ability-container">
        <div className="ability-header">
          <PanelTitle
            title="Intricate Documents,&#10;Now Actionable Knowledge"
            subtitle="No-code solution that turns your messiest documents into your most
            reliable source of truth."
            size="small"
            margin={100}
          />
        </div>
        <div className="ability-content">
          {abilityData.map((item) => (
            <div key={item.title} className="ability-item">
              <div className="image-side">
                <GatsbyImage
                  image={item.image.childImageSharp.gatsbyImageData}
                  className="item-image"
                  alt={item.image.name}
                />
              </div>
              <div className="main">
                <div className="main-content">
                  <div className="item-title">{item.title}</div>
                  <div className="item-desc">{item.desc}</div>
                </div>
              </div>
              <GatsbyImage
                image={item.corner.childImageSharp.gatsbyImageData}
                className="corner-image"
                alt={item.corner.name}
              />
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

Ability.propTypes = {};

export default Ability;
