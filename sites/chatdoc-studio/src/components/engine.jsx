import React from 'react';
import { Container } from 'react-bootstrap';
import '../styles/engine.less';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import PanelTitle from './panel-title';
import { GatsbyImage } from 'gatsby-plugin-image';
import EngineProcessConnectLine from '../assets/icons/process-connect-line.svg';
import ParserPreview from './parser-preview';

const Engine = ({}) => {
  const { engineProcessData, parserBgImage } = useGlobalContext();

  return (
    <div className="engine-wrapper">
      <Container className="engine-container">
        <div className="engine-header">
          <PanelTitle
            title="True Accuracy,&#10;Thanks to Our Superior Engine."
            size="large"
            margin={140}
          />
        </div>
        <div className="engine-content">
          <div className="engine-process">
            <PanelTitle
              title="Contextual Retrieval for High-Quality Q&A"
              subtitle="Go beyond simple keyword search. Captures the structure and context that others miss,&#10;significantly enhancing decision-making processes."
              size="small"
              margin={80}
            />
            <div className="engine-process-chart">
              {engineProcessData.map((item, index, array) => (
                <React.Fragment key={item.label}>
                  <div className="engine-process-item">
                    <GatsbyImage
                      image={item.image.childImageSharp.gatsbyImageData}
                      alt={item.image.name}
                      className="engine-process-item-image"
                    />
                    <div className="engine-process-item-label">{`${
                      index + 1
                    }. ${item.label}`}</div>
                  </div>
                  {index < array.length - 1 && <EngineProcessConnectLine />}
                </React.Fragment>
              ))}
            </div>
          </div>
          <div className="engine-parser">
            <PanelTitle
              title="PDF Parser for Document-Intensive Organizations"
              subtitle="Legacy Archives, Device Manuals, Blueprints with footnotes, submission forms that span pages, our parser sees it all."
              size="small"
              margin={80}
            />
            <ParserPreview />
          </div>
        </div>
      </Container>
    </div>
  );
};

Engine.propTypes = {};

export default Engine;
