import React from 'react';
import PropTypes from 'prop-types';
import '../styles/panel-title.less';
import classnames from 'classnames';

const PanelTitle = ({ title, subtitle, size = 'large', margin = 100 }) => {
  return (
    <div
      className={classnames({
        'panel-title-wrapper': true,
        [size]: true,
      })}
      style={{ margin: `${margin}px 0` }}>
      <div className="panel-title">{title}</div>
      {subtitle && <div className="panel-subtitle">{subtitle}</div>}
    </div>
  );
};

PanelTitle.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  size: PropTypes.string, // large | small
};

export default PanelTitle;
