import React from 'react';
import { Container } from 'react-bootstrap';
import '../styles/support.less';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import PanelTitle from './panel-title';

const Support = ({}) => {
  const { supportData } = useGlobalContext();

  return (
    <div className="support-wrapper">
      <Container className="support-container">
        <div className="support-header">
          <PanelTitle
            title="The one source that&#10;your team can’t work without."
            size="large"
            margin={140}
          />
        </div>
        <div className="support-content">
          {supportData.map((item) => (
            <div key={item.title} className="support-item">
              <img src={item.icon.publicURL} alt={item.title} />
              <div className="support-title">{item.title}</div>
              <div className="support-desc">{item.desc}</div>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

Support.propTypes = {};

export default Support;
