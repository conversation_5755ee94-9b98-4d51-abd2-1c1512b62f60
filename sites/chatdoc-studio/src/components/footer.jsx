import React from 'react';
import '../styles/footer.less';
import ProjectButton from './project-button';
import { getUrlWithProduct } from '../../../../common/urls';
import { getEnvVariables } from '../../../../common/utils/env';

const Footer = () => {
  const { product } = getEnvVariables();
  return (
    <footer className="footer-wrapper">
      <div className="bg">
        <div className="bg-inner"></div>
      </div>
      <div className="content">
        <p className="desc">Businesses Interact</p>
        <p className="desc">with Documents</p>
        <ProjectButton />
      </div>
      <div className="legal">
        <a
          target="_blank"
          href={getUrlWithProduct(product, 'terms')}
          className="legal-item"
          rel="noreferrer">
          Terms of Service
        </a>
        <a
          target="_blank"
          href={getUrlWithProduct(product, 'policy')}
          className="legal-item"
          rel="noreferrer">
          Privacy Policy
        </a>
      </div>
    </footer>
  );
};

export default Footer;
