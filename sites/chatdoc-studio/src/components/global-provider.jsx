import React from 'react';
import { graphql, useStaticQuery } from 'gatsby';
import I18n from '../../../../common/components/i18n/i18n';
import ChatDOCLogo from '../../../../common/assets/logo/chatdoc-studio.png';
import { GlobalContext } from '../../../../common/hooks/useGlobalHook';

const GlobalProvider = ({ children, blogValues }) => {
  const {
    notFoundImage,
    gitCommitData,
    bannerFileIcons,
    bannerBgImage,
    bannerLeftImage,
    bannerRight1Image,
    bannerRight2Image,
    abilityData,
    engineProcessData,
    parserBgImage,
    agentData,
    agentBgImage,
    supportData,
  } = useStaticQuery(query);

  const globalContextValues = {
    defaultSrc: '',
    logo: ChatDOCLogo,
    gitCommitData: gitCommitData,
    linkButtonText: 'Try for Free',
    notFoundImage,
    bannerFileIcons: bannerFileIcons.nodes[0].childrenBannerFileIconsJson,
    bannerBgImage,
    bannerLeftImage,
    bannerRight1Image,
    bannerRight2Image,
    abilityData: abilityData.nodes[0].childrenAbilityJson,
    engineProcessData: engineProcessData.nodes[0].childrenEngineProcessJson,
    agentData: agentData.nodes[0].childrenAgentJson,
    supportData: supportData.nodes[0].childrenSupportJson,
    parserBgImage,
    agentBgImage,
  };

  return (
    <GlobalContext.Provider value={globalContextValues}>
      <I18n>{children}</I18n>
    </GlobalContext.Provider>
  );
};

const query = graphql`
  query {
    gitCommitData: gitCommit(latest: { eq: true }) {
      hash
      date
    }
    notFoundImage: file(base: { eq: "404.png" }) {
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerBgImage: file(base: { eq: "banner-bg.png" }) {
      name
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerLeftImage: file(base: { eq: "banner-left.png" }) {
      name
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerRight1Image: file(base: { eq: "banner-right-1.png" }) {
      name
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerRight2Image: file(base: { eq: "banner-right-2.png" }) {
      name
      childImageSharp {
        gatsbyImageData
      }
    }
    bannerFileIcons: allFile(
      filter: {
        absolutePath: { regex: "/banner-file-icons/banner-file-icons/" }
      }
    ) {
      nodes {
        childrenBannerFileIconsJson {
          name
          icon {
            publicURL
          }
        }
      }
    }
    abilityData: allFile(
      filter: { absolutePath: { regex: "/ability/ability/" } }
    ) {
      nodes {
        childrenAbilityJson {
          title
          desc
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          corner {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    engineProcessData: allFile(
      filter: { absolutePath: { regex: "/engine-process/engine-process/" } }
    ) {
      nodes {
        childrenEngineProcessJson {
          label
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    parserBgImage: file(base: { eq: "parser-bg.png" }) {
      name
      childImageSharp {
        gatsbyImageData
      }
    }
    agentData: allFile(filter: { absolutePath: { regex: "/agent/agent/" } }) {
      nodes {
        childrenAgentJson {
          title
          desc
          icon {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
    agentBgImage: file(base: { eq: "agent-bg.png" }) {
      name
      childImageSharp {
        gatsbyImageData
      }
    }
    supportData: allFile(
      filter: { absolutePath: { regex: "/support/support/" } }
    ) {
      nodes {
        childrenSupportJson {
          title
          desc
          icon {
            publicURL
          }
        }
      }
    }
  }
`;

export default GlobalProvider;
