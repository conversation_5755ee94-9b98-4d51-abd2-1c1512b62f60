import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import SuggestBrowserTip from '../../../../common/components/suggestBrowserTip/index';
import Header from './header';
import Footer from './footer';
import { useGetUrlQueryData } from '../../../../common/hooks/useUrlQueryHook';
import { setChatDOCUID } from '../../../../common/utils/chatdoc/chatdoc-uid';

const Layout = ({ className, children }) => {
  const src = useGetUrlQueryData('src');

  useEffect(() => {
    setChatDOCUID();
  }, []);

  return (
    <div className={className}>
      <SuggestBrowserTip locale="en" headerClass={'header-wrapper'} />
      <Header />
      {children}
      <Footer />
    </div>
  );
};

Layout.defaultProps = {
  hideAnnouncement: true,
};

Layout.propTypes = {
  hideFooter: PropTypes.bool,
  hideAnnouncement: PropTypes.bool,
  currentHideFeatureInHeader: PropTypes.bool,
  isIndexPage: PropTypes.bool,
  className: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

export default Layout;
