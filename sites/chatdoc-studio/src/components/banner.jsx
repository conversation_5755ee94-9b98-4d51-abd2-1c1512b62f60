import React from 'react';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import '../styles/banner.less';
import ProjectButton from './project-button';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import BannerLinearSvg from '../assets/icons/banner-linear.svg';

const Banner = ({}) => {
  const {
    bannerFileIcons,
    bannerBgImage,
    bannerLeftImage,
    bannerRight1Image,
    bannerRight2Image,
  } = useGlobalContext();

  return (
    <div className="banner-wrapper">
      <Container className="banner-container">
        <div className="banner-desc">
          <div className="banner-desc-sub">AI Agent that Transforms how</div>
          <div className="banner-desc-main">
            <span className="banner-desc-main-linear">
              Businesses
              <BannerLinearSvg className="banner-desc-linear" />
              <div className="banner-desc-file-icons">
                {bannerFileIcons.map((item) => (
                  <img
                    key={item.icon.publicURL}
                    alt={item.name}
                    src={item.icon.publicURL}
                  />
                ))}
              </div>
            </span>
            <span> Interact</span>
            <div className="banner-desc-main-last-row">with Documents</div>
          </div>
          <div className="banner-desc-detail">
            Enterprise-grade AI assistants that deliver highly accurate answers
            from most complex documents, with 100% traceable insights.
          </div>
          <ProjectButton />
        </div>
        <div className="banner-images">
          <GatsbyImage
            image={bannerBgImage.childImageSharp.gatsbyImageData}
            className="bg"
            alt={bannerBgImage.name}
          />
          <GatsbyImage
            image={bannerLeftImage.childImageSharp.gatsbyImageData}
            className="left"
            alt={bannerLeftImage.name}
          />
          <GatsbyImage
            image={bannerRight1Image.childImageSharp.gatsbyImageData}
            className="right1"
            alt={bannerRight1Image.name}
          />
          <GatsbyImage
            image={bannerRight2Image.childImageSharp.gatsbyImageData}
            className="right2"
            alt={bannerRight2Image.name}
          />
        </div>
      </Container>
    </div>
  );
};

Banner.propTypes = {};

export default Banner;
