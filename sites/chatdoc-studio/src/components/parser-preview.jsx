import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Container, Form, InputGroup, Nav, NavItem } from 'react-bootstrap';
import { Slide, toast } from 'react-toastify';
import ClipboardJS from 'clipboard';
import classnames from 'classnames';
import { useMobilePageContext } from '../../../../common/hooks/useMobilePageContext';
import { useHandleResize } from '../../../../common/hooks/useHandleResizeHook';
import 'react-toastify/dist/ReactToastify.css';
import { getDocListApi } from '../../../../common/api/chatdoc-studio';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import '../styles/parser-preview.less';
import { GatsbyImage } from 'gatsby-plugin-image';

const checksumList = [
  '614b23e2-175a-4f72-8ab1-ba77cead9403',
  '7e8c3cda-1ade-4800-8f1d-03621c5c0ab0',
  '033db456-136d-43c4-ad45-a4f7e9f69cb0',
];

const exampleNameMap = {
  0: 'Annual Report',
  1: 'ArXiv Paper',
  2: 'Project Plan',
};

const Preview = () => {
  const { defaultSrc, parserBgImage } = useGlobalContext();

  const isMobile = useMobilePageContext();
  const iframeRef = useRef();
  const [linkText, setLinkText] = useState('');
  const [navItemWidth, setNavItemWidth] = useState([]);
  const [navList, setNavList] = useState([]);
  const [currentIframeUrl, setCurrentIframeUrl] = useState('');
  const [tabActived, setTabActived] = useState(0);

  const scrollToFeaturePanel = () => {
    const titleEl = document.querySelector('.feature-container .feature-title');
    setTimeout(() => {
      titleEl.scrollIntoView({ behavior: 'smooth' });
    });
  };

  useEffect(() => {
    const toastOptions = {
      position: 'top-center',
      autoClose: 1e3,
      transition: Slide,
      hideProgressBar: true,
      closeButton: false,
      pauseOnFocusLoss: false,
    };

    setLinkText(`${window.location.origin}?target=feature`);

    const clipboard = new ClipboardJS('.link-btn');
    clipboard.on('success', () => {
      toast.success('URL Copied.', toastOptions);
    });
    clipboard.on('error', () => {
      toast.error('Copy failed, please try again.', toastOptions);
    });

    return () => {
      clipboard.destroy();
    };
  }, []);

  useEffect(() => {
    const target = new URLSearchParams(window.location.search).get('target');
    if (target === 'feature') {
      scrollToFeaturePanel();
    }
  }, []);

  useEffect(() => {
    if (!currentIframeUrl) {
      return;
    }
    iframeRef.current?.contentWindow.location.replace(currentIframeUrl);
  }, [currentIframeUrl]);

  const getNavItemSize = useCallback(() => {
    const navItemList = Array.from(
      document.querySelectorAll('.feature-nav>.feature-item'),
    );
    setNavItemWidth(navItemList.map((el) => el.offsetWidth));
  }, []);

  const getDocList = useCallback(async () => {
    if (isMobile) {
      return;
    }
    const urlSrc = new URLSearchParams(window.location.search).get('src');
    const res = await getDocListApi();
    const list = res.map((item, index) => ({
      title: exampleNameMap[index],
      url: `https://pdfparser.dev.cheftin.cn/pdfparser/#/view/${
        item.upload_id
      }?show_back=false&show_header=false&checksum=${checksumList[index]}&src=${
        urlSrc || defaultSrc
      }`,
    }));

    setNavList(list);
    setCurrentIframeUrl(list[0].url);
  }, [defaultSrc, isMobile]);

  const selectTab = (item, index) => {
    setTabActived(index);
    setCurrentIframeUrl(item.url);
  };

  useEffect(() => {
    getDocList();
  }, [getDocList]);

  useEffect(() => {
    getNavItemSize();
  }, [getNavItemSize, navList]);

  useHandleResize(() => {
    getNavItemSize();
  });

  const navActivedStyle = useMemo(
    () => ({
      width: navItemWidth[tabActived] + 'px',
      left:
        navItemWidth
          .slice(0, tabActived)
          .reduce((sum, width) => sum + width, 0) + 'px',
    }),
    [tabActived, navItemWidth],
  );

  return (
    <div className="preview-wrapper">
      <Container className="preview-container">
        <div className="preview-box feature-box">
          {!isMobile && (
            <>
              <Nav
                className={classnames({
                  'feature-nav': true,
                })}>
                <div className="active-bg" style={navActivedStyle}></div>
                {navList.map((item, index) => (
                  <NavItem
                    key={index}
                    className={classnames({
                      'feature-item': true,
                      active: index === tabActived,
                    })}
                    onClick={() => selectTab(item, index)}>
                    {item.title}
                  </NavItem>
                ))}
              </Nav>
            </>
          )}
          <div className="feature-content">
            {!isMobile ? (
              <div className="feature-content-main">
                <iframe ref={iframeRef} className="iframe" frameBorder="0" />
              </div>
            ) : (
              <div className="feature-content-main-mobile">
                <div className="mobile-link">
                  <InputGroup className="link-input-group">
                    <Form.Control
                      type="text"
                      name="link"
                      readOnly
                      value={linkText}
                      className="link-input"
                    />
                    <button
                      className="default-btn link-btn"
                      data-clipboard-action="copy"
                      data-clipboard-target=".link-input">
                      Copy
                    </button>
                  </InputGroup>
                  <p className="link-desc">
                    * You can view the JSON parsing results on the computer.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
        <GatsbyImage
          image={parserBgImage.childImageSharp.gatsbyImageData}
          alt={parserBgImage.name}
          className="parser-bg-image"
        />
      </Container>
    </div>
  );
};

export default Preview;
