import React from 'react';
import { Container } from 'react-bootstrap';
import '../styles/agent.less';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import PanelTitle from './panel-title';
import { GatsbyImage } from 'gatsby-plugin-image';

const Agent = ({}) => {
  const { agentData, agentBgImage } = useGlobalContext();

  return (
    <div className="agent-wrapper">
      <div className="agent-header">
        <PanelTitle
          title="Your AI Agent,&#10;goes live in 4 simple steps."
          size="large"
          margin={140}
        />
      </div>
      <div className="agent-content">
        {agentData.map((item) => (
          <div key={item.desc} className="agent-item">
            <div className="agent-item-main">
              <div className="agent-item-icon">
                <GatsbyImage
                  image={item.icon.childImageSharp.gatsbyImageData}
                  className="item-image"
                  alt={item.icon.name}
                />
              </div>
              <div className="agent-item-title">{item.title}</div>
              <div className="agent-item-desc">{item.desc}</div>
              <div className="agent-item-image">
                <GatsbyImage
                  image={item.image.childImageSharp.gatsbyImageData}
                  alt={item.image.name}
                />
              </div>
            </div>
            <GatsbyImage
              image={agentBgImage.childImageSharp.gatsbyImageData}
              alt={agentBgImage.name}
              className="agent-bg-image"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

Agent.propTypes = {};

export default Agent;
