import React from 'react';
import 'bootstrap/dist/css/bootstrap.css';
import '../../common/styles/reset.less';
import '../../common/styles/chatdoc/common.less';
import GlobalProvider from './src/components/global-provider';
import { MobilePageContext } from '../../common/hooks/useMobilePageContext';

function testFeatures(window) {
  const isIntersectionObserverSupported =
    'IntersectionObserver' in window &&
    'IntersectionObserverEntry' in window &&
    'intersectionRatio' in window.IntersectionObserverEntry.prototype;

  return {
    isIntersectionObserverSupported,
  };
}

window.__browserFeatures = testFeatures(window);

export const onClientEntry = () => {
  if (/notFound/.test(window.pagePath)) {
    window.pagePath = '';
  }
};

export const wrapPageElement = ({ element, props }) => {
  const { isMobilePage } = props.pageContext;

  return (
    <GlobalProvider>
      <MobilePageContext.Provider
        value={{ isMobilePage: Boolean(isMobilePage) }}>
        {element}
      </MobilePageContext.Provider>
    </GlobalProvider>
  );
};
