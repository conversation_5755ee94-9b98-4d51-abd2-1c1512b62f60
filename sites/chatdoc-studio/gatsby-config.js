require('dotenv').config({
  path: `.env.${process.env.SITE_ENV}`,
});

const path = require('path');
const site = 'chatdoc-studio';
const proxy = 'https://chatdoc-studio.test.paodingai.com/chatdoc-studio/';
const analyseBundles = Boolean(process.env.ANALYSE_BUNDLES);

const { getEnvVariables } = require('../../common/utils/env.js');
const {
  isTestEnv,
  isProdEnv,
  isStagingEnv,
  productUrl,
  clarityId,
  googleAdsId,
  googleAnalyticsId,
  googleTagmanagerId,
} = getEnvVariables();

function createBuildRoot(site) {
  if (site) {
    return `${__dirname}/src/pages`;
  }
  return '';
}

const manifest = require(`${__dirname}/src/manifest.js`) || {};
var { createProxyMiddleware } = require('http-proxy-middleware');

const plugins = [
  {
    resolve: 'gatsby-plugin-page-creator',
    options: {
      path: createBuildRoot(site),
      // ignore: ['manifest.js', '**/components/*']
    },
  },
  'gatsby-transformer-json',
  'gatsby-plugin-image',
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: path.resolve(__dirname, './src/assets/images'),
    },
  },
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: `${__dirname}/static`,
    },
  },
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: `${__dirname}/content`,
    },
  },
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: path.resolve(__dirname, '../../common/content'),
    },
  },
  {
    resolve: `gatsby-source-filesystem`,
    options: {
      path: path.resolve(__dirname, '../../common/static/chatdoc-studio'),
    },
  },
  {
    resolve: 'gatsby-plugin-copy-files',
    options: {
      source: path.resolve(__dirname, `../../common/static/chatdoc-studio`),
      destination: '',
    },
  },
  {
    resolve: 'gatsby-plugin-react-svg',
    options: {
      rule: {
        include: [
          path.resolve(__dirname, 'src/assets/icons'),
          path.resolve(__dirname, '../../common/assets/icons'),
        ],
      },
    },
  },
  {
    resolve: `gatsby-transformer-sharp`,
    options: {
      checkSupportedExtensions: false,
    },
  },
  {
    resolve: `gatsby-plugin-sharp`,
    options: {
      defaults: {
        placeholder: 'none',
      },
    },
  },
  {
    resolve: `gatsby-plugin-less`,
    options: {
      lessOptions: {
        modifyVars: require(`../../common/styles/theme.js`),
      },
    },
  },
  {
    resolve: `gatsby-plugin-postcss`,
    options: {
      postCssPlugins: [
        require(`postcss-preset-env`)({
          stage: 0,
        }),
        require('postcss-nested'),
        require('postcss-calc'),
      ],
      cssLoaderOptions: {
        camelCase: false,
      },
    },
  },
  {
    resolve: 'gatsby-plugin-no-sourcemaps',
  },
  'gatsby-plugin-remove-serviceworker',
  'gatsby-source-local-git',
  {
    resolve: 'gatsby-plugin-antd',
    options: {
      style: true,
    },
  },
  {
    resolve: `gatsby-transformer-remark`,
    options: {
      // Footnotes mode (default: true)
      footnotes: true,
      // GitHub Flavored Markdown mode (default: true)
      gfm: true,
      // Plugins configs
      plugins: [
        {
          resolve: `gatsby-remark-images`,
          options: {
            // It's important to specify the maxWidth (in pixels) of
            // the content container as this plugin uses this as the
            // base for generating different widths of each image.
            quality: 100,
            wrapperStyle:
              'max-width: 100%!important; border: 1px solid #EAEDF3;',
          },
        },
        {
          resolve: 'gatsby-remark-external-links', // markdown 链接默认在新窗口打开
        },
        {
          resolve: `gatsby-remark-highlight-code`,
        },
      ],
    },
  },
];

if (clarityId) {
  plugins.push({
    resolve: 'gatsby-plugin-clarity',
    options: { clarity_project_id: clarityId },
  });
}

if (googleTagmanagerId) {
  plugins.push({
    resolve: 'gatsby-plugin-google-tagmanager',
    options: { id: googleTagmanagerId },
  });
}

const trackingIds = [];
if (googleAdsId) {
  trackingIds.push(googleAdsId);
}
if (googleAnalyticsId) {
  trackingIds.push(googleAnalyticsId);
}
if (trackingIds.length > 0) {
  plugins.push({
    resolve: 'gatsby-plugin-google-gtag',
    options: { trackingIds },
  });
}

if (analyseBundles) {
  plugins.push('gatsby-plugin-perf-budgets');
  plugins.push('gatsby-plugin-webpack-bundle-analyser-v2');
}

if (isProdEnv) {
  plugins.push({
    resolve: 'gatsby-plugin-robots-txt',
    options: {
      policy: [{ userAgent: '*', allow: ['/'] }],
      sitemap: [
        `${productUrl}/sitemap-index.xml`,
        `${productUrl}/chat/sitemap.xml`,
      ],
    },
  });
} else {
  plugins.push({
    resolve: 'gatsby-plugin-robots-txt',
    options: {
      policy: [{ userAgent: '*', disallow: ['/'] }],
      sitemap: null,
    },
  });
}

module.exports = {
  assetPrefix: `https://cdn.pdppt.com/chatdoc-studio`,
  siteMetadata: {
    currentSite: site,
    siteUrl: productUrl || 'https://www.paodingai.com/',
  },
  plugins,
  developMiddleware: (app) => {
    app.use(
      '/chatdoc-studio/api/v1',
      createProxyMiddleware({
        target: proxy || '/',
        pathRewrite: { '^/chatdoc-studio': '' },
        changeOrigin: true,
        secure: false, // Do not reject self-signed certificates.
      }),
    );
    app.use(
      '/crm',
      createProxyMiddleware({
        target: proxy || '/',
        changeOrigin: true,
        secure: false, // Do not reject self-signed certificates.
      }),
    );

    // 本地环境调试
    // app.use(
    //   '/crm',
    //   createProxyMiddleware({
    //     target: 'http://localhost:3000/',
    //     pathRewrite: { '^/crm': '' },
    //     changeOrigin: true,
    //     secure: false, // Do not reject self-signed certificates.
    //   }),
    // );
    // app.use(
    //   '/Users/<USER>/work/front_crm',
    //   createProxyMiddleware({
    //     target: 'http://localhost:3000/',
    //     changeOrigin: true,
    //     secure: false,
    //     ws: true,
    //   }),
    // );
    app.use(
      '/chatdoc',
      createProxyMiddleware({
        target: proxy || '/',
        changeOrigin: true,
        secure: false, // Do not reject self-signed certificates.
      }),
    );
  },
};
