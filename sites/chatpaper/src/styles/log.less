.chatpaper-log-page {
  position: relative;
  font-weight: 400;
  font-family: <PERSON><PERSON><PERSON>;

  &::after {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);
    content: '';
  }

  .footer-wrapper {
    margin-top: 258px;
  }
}

@media (max-width: @small-screen-size) {
  .chatpaper-log-page {
    .footer-wrapper {
      margin-top: 50px;
    }
  }
}
