.banner-button {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 30px;
  align-items: center;
  justify-content: center;

  .banner-button-paper,
  .banner-button-chat {
    padding: 14px 44px;
    color: #6576db;
    font-weight: 500;
    font-size: 24px;
    font-family: Poppins;
    line-height: 33px;
    border: 1.5px solid #6576db;
    border-radius: 8px;
  }

  .banner-button-paper {
    background-color: rgba(#6576db, 0.1);
  }

  .banner-button-chat {
    background-color: transparent;

    .project-button-text {
      background-image: linear-gradient(
        220deg,
        rgba(157, 157, 249, 90%) 3.61%,
        rgba(29, 69, 194, 90%) 87.27%
      );
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .banner-button {
    .banner-button-paper,
    .banner-button-chat {
      padding: 9px 29px;
      font-size: 16px;
      line-height: 22px;
      border-width: 1px;
      border-radius: 6px;
    }
  }
}

@media (max-width: @least-screen-size) {
  .banner-button {
    flex-direction: column;
  }
}
