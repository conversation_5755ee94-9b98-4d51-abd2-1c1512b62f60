SHELL := /bin/bash

UID := $(shell id -u)
GID := $(shell id -g)
PWD := $(shell pwd)
PREFIX := build_task_to_
IMAGE := registry.cheftin.cn/p/node18

# pdflux-en
# change target by `make target=semanmeter all`
target := paodingai autodoc calliper calliper_vip glazer grater hunter pdflux chatdoc \
       pdflux-sdk scriber semanmeter chatdb paodingjiewen online_soon pdfparser pdfchat \
       metalmesh chatdoc-studio

ifneq ($(BUILD_CACHE_DIR),)
CACHE_DIR := $(BUILD_CACHE_DIR)
else ifneq ($(GO_PIPELINE_NAME),)
CACHE_DIR := $(PWD)/../../$(GO_PIPELINE_NAME)_cache
else
CACHE_DIR := $(PWD)/../cache/paoding-officialweb
endif

DOCKER_RUN_ARGS := --rm --pull always --init -v $(PWD):/opt/src/ \
		-e NODE_OPTIONS="--max-old-space-size=3072" \
		-u $(UID):$(GID) $(IMAGE)

.PHONY : clean all
.DEFAULT_GOAL := all

# 将不带前缀的 target 刷成 build_task_to_BUILD_XXX
define add_build_target_prefix
$(if $(filter $(PREFIX)%,$(1)),$(subst $(PREFIX),,$(1)),$(1))
endef

# 生成 build_task_to_BUILD_XXX 和 XXX 的 target
define make_build_target
.PHONY: $(PREFIX)$(1)
$(PREFIX)$(1):
	mkdir -p $(CACHE_DIR)/$(1)/node_modules $(CACHE_DIR)/$(1)/yarn_cache
	docker run -e BUILD_SCRIPT=build_$(1).sh -e BUILD_POSTFIX=$(BUILD_POSTFIX) \
		-e ENABLE_PDFLUX_CDN=$(ENABLE_PDFLUX_CDN) -e ENABLE_CHATDOC_CDN=$(ENABLE_CHATDOC_CDN) \
		-e ENABLE_PDFCHAT_CDN=$(ENABLE_PDFCHAT_CDN) -e ENABLE_CHATPAPER_CDN=$(ENABLE_CHATPAPER_CDN) \
		-e ENABLE_CHATDOC_STUDIO_CDN=$(ENABLE_CHATDOC_STUDIO_CDN) \
		-v $(CACHE_DIR)/$(1)/node_modules:/opt/build/node_modules \
	    -v $(CACHE_DIR)/$(1)/yarn_cache:/tmp/.cache \
		$(DOCKER_RUN_ARGS) \
			auto_retry.sh "cd /opt/src/ && make exec_in_container" 2

.PHONY: $(1)
$(1):
	$(MAKE) $(PREFIX)$(call add_build_target_prefix,$(t))
endef

$(foreach t,$(target),$(eval $(call make_build_target,$(call add_build_target_prefix,$(t)))))

all:
	$(MAKE) clean
	$(MAKE) build_tasks

exec_in_container:
	rsync -rlD --exclude=/public-* --exclude=/sites/*/public --exclude=/node_modules /opt/src/ /opt/build/ && \
	cd /opt/build && ./ci/${BUILD_SCRIPT} && \
	rsync -a /opt/build/public* /opt/src/

build_tasks: $(foreach t,$(target),$(call add_build_target_prefix,$(t)))

clean:
	-rm -rf public-*
	-git lfs pull

.PHONY: list
list:
	@LC_ALL=C $(MAKE) -pRrq -f $(firstword $(MAKEFILE_LIST)) : 2>/dev/null | awk -v RS= -F: '/(^|\n)# Files(\n|$$)/,/(^|\n)# Finished Make data base/ {if ($$1 !~ "^[#.]") {print $$1}}' | sort | grep -E -v -e '^[^[:alnum:]]' -e '^$@$$'
