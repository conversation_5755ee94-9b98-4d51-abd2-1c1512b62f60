const SOURCE = {
  OFFICIAL_WEBSITE: -1, // 公司官网
  AUTODOC_VIP: 0, // AutoDoc 企业版
  PDFLUX: 1, // PDFlux
  GLAZER: 2, // Glazer
  CALLIPER_PERSONAL: 3, // Calliper 个人版
  SCRIBER: 4, // Scriber
  ROSTER: 5, // Roster
  AUTODOC_PERSONAL: 6, // AutoDoc 个人版
  GRATER: 7, // Grater
  CALLIPER_VIP: 8, // Calliper 企业版
  HUNTER: 9, // Hunter
  FOUNDRY: 10, // Foundry
  PDFLUX_SDK: 11, // PDFlux SDK
  AI_PLATFORM: 12, // AI Platform
  SEMANMETER: 13, // Semanmeter
  DATA_INTELLIGENCE_PLATFORM: 14, // 数据智能平台
  CHATDOC: 15, // ChatDOC
  CHATPAPER: 15, // ChatPaper
  CHATITDONE: 16, // Chat It Done
  CHATDOC_API: 17, // ChatDOC API
  PAO_DING_JIE_WEN: 18, // 庖丁解文
  CHATDB: 19, // ChatDB
  CHATDB_API: 20, // ChatDB API
  PDF_PARSER: 21, // PDF PARSER
  PDFCHAT: 22, // PDF Chat
  METALMESH: 23, // MetalMesh
  AUTODOC_NEXT: 24, // AutoDoc Next
  CHATDOC_STUDIO: 25, // ChatDOC Studio
};

const SOURCE_MAP = {
  [SOURCE.PDFLUX]: 'PDFlux',
  [SOURCE.GLAZER]: 'Glazer',
  [SOURCE.CALLIPER_PERSONAL]: 'Calliper',
  [SOURCE.CALLIPER_VIP]: 'Calliper VIP',
  [SOURCE.SCRIBER]: 'Scriber',
  [SOURCE.AUTODOC_PERSONAL]: 'AutoDoc',
  [SOURCE.AUTODOC_VIP]: 'AutoDoc VIP',
  [SOURCE.AUTODOC_NEXT]: 'AutoDoc Next',
  [SOURCE.GRATER]: 'Grater',
  [SOURCE.HUNTER]: 'Hunter',
  [SOURCE.PDFLUX_SDK]: 'PDFlux SDK',
  [SOURCE.FOUNDRY]: 'Foundry',
  [SOURCE.AI_PLATFORM]: 'AI Platform',
  [SOURCE.SEMANMETER]: 'Semanmeter',
  [SOURCE.DATA_INTELLIGENCE_PLATFORM]: '数据智能平台',
  [SOURCE.CHATDOC]: 'ChatDOC',
  [SOURCE.CHATITDONE]: 'Chat It Done',
  [SOURCE.CHATDOC_API]: 'ChatDOC API',
  [SOURCE.PAO_DING_JIE_WEN]: '庖丁解文',
  [SOURCE.CHATDB]: 'ChatDB',
  [SOURCE.CHATDB_API]: 'ChatDB API',
  [SOURCE.PDF_PARSER]: 'PDF PARSER',
  [SOURCE.PDFCHAT]: 'PDFChat',
  [SOURCE.METALMESH]: 'MetalMesh',
  [SOURCE.CHATDOC_STUDIO]: 'ChatDOC Studio',
};

const PRODUCTS_MAP = {
  autodoc: SOURCE_MAP[SOURCE.AUTODOC_PERSONAL],
  'autodoc-vip': SOURCE_MAP[SOURCE.AUTODOC_VIP],
  'autodoc-next': SOURCE_MAP[SOURCE.AUTODOC_NEXT],
  calliper: SOURCE_MAP[SOURCE.CALLIPER_PERSONAL],
  'calliper-vip': SOURCE_MAP[SOURCE.CALLIPER_VIP],
  chatdb: SOURCE_MAP[SOURCE.CHATDB],
  chatdoc: SOURCE_MAP[SOURCE.CHATDOC],
  chatitdone: SOURCE_MAP[SOURCE.CHATITDONE],
  glazer: SOURCE_MAP[SOURCE.GLAZER],
  grater: SOURCE_MAP[SOURCE.GRATER],
  'grater-vip': SOURCE_MAP[SOURCE.GRATER],
  hunter: SOURCE_MAP[SOURCE.HUNTER],
  metalmesh: SOURCE_MAP[SOURCE.METALMESH],
  paodingjiewen: SOURCE_MAP[SOURCE.PAO_DING_JIE_WEN],
  pdfchat: SOURCE_MAP[SOURCE.PDFCHAT],
  pdflux: SOURCE_MAP[SOURCE.PDFLUX],
  'pdflux-sdk': SOURCE_MAP[SOURCE.PDFLUX_SDK],
  pdfparser: SOURCE_MAP[SOURCE.PDF_PARSER],
  scriber: SOURCE_MAP[SOURCE.SCRIBER],
  semanmeter: SOURCE_MAP[SOURCE.SEMANMETER],
  chatpaper: 'ChatPaper',
  'chatdoc-studio': SOURCE_MAP[SOURCE.CHATDOC_STUDIO],
};

const SOURCE_ARRAY = [
  {
    label: SOURCE_MAP[SOURCE.PDFLUX],
    value: SOURCE.PDFLUX,
  },
  {
    label: SOURCE_MAP[SOURCE.GLAZER],
    value: SOURCE.GLAZER,
  },
  {
    label: SOURCE_MAP[SOURCE.CALLIPER_PERSONAL],
    value: SOURCE.CALLIPER_PERSONAL,
  },
  {
    label: SOURCE_MAP[SOURCE.CALLIPER_VIP],
    value: SOURCE.CALLIPER_VIP,
  },
  {
    label: SOURCE_MAP[SOURCE.SCRIBER],
    value: SOURCE.SCRIBER,
  },
  {
    label: SOURCE_MAP[SOURCE.AUTODOC_PERSONAL],
    value: SOURCE.AUTODOC_PERSONAL,
  },
  {
    label: SOURCE_MAP[SOURCE.AUTODOC_VIP],
    value: SOURCE.AUTODOC_VIP,
  },
  {
    label: SOURCE_MAP[SOURCE.AUTODOC_NEXT],
    value: SOURCE.AUTODOC_NEXT,
  },
  {
    label: SOURCE_MAP[SOURCE.GRATER],
    value: SOURCE.GRATER,
  },
  {
    label: SOURCE_MAP[SOURCE.HUNTER],
    value: SOURCE.HUNTER,
  },
  {
    label: SOURCE_MAP[SOURCE.PDFLUX_SDK],
    value: SOURCE.PDFLUX_SDK,
  },
  {
    label: SOURCE_MAP[SOURCE.FOUNDRY],
    value: SOURCE.FOUNDRY,
  },
  {
    label: SOURCE_MAP[SOURCE.AI_PLATFORM],
    value: SOURCE.AI_PLATFORM,
  },
  {
    label: SOURCE_MAP[SOURCE.SEMANMETER],
    value: SOURCE.SEMANMETER,
  },
  {
    label: SOURCE_MAP[SOURCE.DATA_INTELLIGENCE_PLATFORM],
    value: SOURCE.DATA_INTELLIGENCE_PLATFORM,
  },
  {
    label: SOURCE_MAP[SOURCE.PAO_DING_JIE_WEN],
    value: SOURCE.PAO_DING_JIE_WEN,
  },
  {
    label: SOURCE_MAP[SOURCE.METALMESH],
    value: SOURCE.METALMESH,
  },
];

module.exports = {
  SOURCE: SOURCE,
  SOURCE_MAP: SOURCE_MAP,
  PRODUCTS_MAP: PRODUCTS_MAP,
  SOURCE_ARRAY: SOURCE_ARRAY,
};
