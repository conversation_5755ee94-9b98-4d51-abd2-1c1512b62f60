.project-button {
  padding: 9px 29px;
  color: #fff;
  font-weight: 500;
  font-size: 16px;
  font-family: Poppins;
  line-height: 22px;
  letter-spacing: normal;
  white-space: nowrap;
  background-color: #6576db;
  border: 1px solid #6576db;
}

.v3-project-button {
  padding: 12px 15px;
  color: #282828;
  background: #fff;
  border: 1px solid rgba(255, 255, 255, 10%);
  border-radius: 100px;

  img {
    margin-left: 8px;
  }

  &:hover {
    color: #6576db;
  }
}

.project-button-animation {
  position: relative;
  width: 263px;
  height: 63px;

  .project-button-border {
    position: absolute;
    top: 2px;
    left: 4px;
    width: calc(100% - 8px);
    height: calc(100% - 4px);
    background-color: #e9eefa;
    border: 1px solid #6576db;
    border-radius: 6px;
  }

  .project-button {
    position: relative;
    z-index: 1;
    display: inline-block;
    width: 100%;
    height: 100%;
    background-color: unset;
    border: none;
  }

  .project-button-text {
    position: absolute;
    top: 15px;
    left: 85px;
    color: #6576db;
    font-weight: 500;
    font-size: 24px;
    font-family: Poppins;
    line-height: 33px;
  }

  .project-button-lottie {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
  }
}

@media (max-width: @small-screen-size) {
  .project-button-animation {
    width: 175px;
    height: 42px;

    .project-button-text {
      top: 10px;
      left: 57px;
      font-size: 16px;
      line-height: 22px;
    }
  }

  .v3-project-button {
    font-size: 16px !important;
  }
}

@media (max-width: @mini-screen-size) {
  .project-button {
    padding: 7px 11px;
    font-size: 14px;
    line-height: 21px;
  }
}
