.features-wrapper {
  --desc-item-margin-left: 20px;

  background-color: #fbfbfb;
  border-bottom: @border-base;

  .feature-item {
    padding: 120px 0;
    border-bottom: @border-base;

    &:nth-child(2n) {
      background-color: #fff;

      .feature-row {
        flex-direction: row-reverse;
        align-items: normal;
      }

      .desc-list {
        width: calc(100% + var(--desc-item-margin-left));
        padding-left: var(--desc-item-margin-left);
        border-right: none;
        border-left: 1px solid #dedede;
      }

      .desc-item {
        padding-right: 15px;
      }

      .desc-item-selected {
        border-radius: 0 30px 30px 0;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .feature-row {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin: 0;
  }

  .feature-content {
    max-width: 480px;
    padding: 0;
  }

  .content-title {
    display: flex;
    align-items: flex-start;
    width: fit-content;
    margin-right: -100px;
    margin-bottom: 24px;
    column-gap: 20px;
  }

  .feature-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    margin-top: 7px;
  }

  .title-text {
    color: @dark-title-color;
    font-weight: 600;
    font-size: 36px;
    font-family: Poppins;
    line-height: 54px;
    letter-spacing: 2px;
  }

  .content-subtitle {
    margin-bottom: 60px;
    color: @dark-text-color;
    font-weight: 500;
    font-size: 22px;
    font-family: Poppins;
    line-height: 33px;
    letter-spacing: 2px;
    white-space: nowrap;
  }

  .desc-list {
    border-right: @border-base;

    li {
      color: @dark-text-color;
      list-style-type: circle;

      &::marker {
        color: #6576db;
      }
    }
  }

  .desc-item {
    margin-left: var(--desc-item-margin-left);
    cursor: pointer;
  }

  .desc-item-text {
    padding: 9px 0 10px 8px;
    color: inherit;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 36px;

    .new-tag {
      position: relative;
      bottom: 2px;
      margin-left: 8px;
      padding: 0 9px;
    }
  }

  .feature-video {
    width: 63%;
    height: 100%;
    padding: 0;
  }

  .video-item {
    width: 100%;
    border: @border-base;
  }
}

@media (max-width: @large-screen-size) {
  .features-wrapper {
    .feature-content {
      width: 37%;
    }

    .content-title {
      margin-right: -40px;
    }

    .title-text {
      font-size: 32px;
      line-height: 40px;
    }

    .feature-icon {
      margin-top: 0;
    }

    .content-subtitle {
      margin-bottom: 50px;
    }

    .feature-video {
      width: 60%;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .features-wrapper {
    .feature-row {
      flex-direction: column;
    }

    .feature-content {
      width: 100%;
      max-width: unset;
      padding-right: 0;
    }

    .content-title {
      align-items: center;
      margin-right: 0;
    }

    .content-subtitle {
      margin-bottom: 30px;
    }

    .feature-item {
      &:nth-child(2n) {
        .desc-list {
          width: 100%;
          padding-left: 0;
          border-right: 1px solid #dedede;
          border-left: none;
        }

        .desc-item-selected {
          margin-left: 0;
          border-radius: 30px 0 0 30px;
        }
      }
    }

    .desc-list {
      margin-bottom: 30px;
    }

    .feature-video {
      width: 100%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .features-wrapper {
    .feature-item {
      padding: 90px 0;
    }

    .title-text {
      font-size: 30px;
      line-height: 32px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .features-wrapper {
    .feature-item {
      padding: 30px 0;

      &:nth-child(2n) {
        .desc-item-selected {
          border-radius: 35px 0 0 35px;
        }
      }
    }

    .feature-content {
      padding: 0;
    }

    .content-title {
      flex-direction: column;
      align-items: normal;
      margin-bottom: 0;
      column-gap: 12px;
    }

    .feature-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 16px;
    }

    .title-text {
      font-size: 24px;
      line-height: 36px;
      letter-spacing: 0.545455px;
    }

    .content-subtitle {
      margin-bottom: 19px;
      font-size: 14px;
      line-height: 21px;
      letter-spacing: 0.545455px;
    }

    .desc-list {
      margin-bottom: 20px;

      li {
        line-height: 20px;
      }
    }

    .desc-item-text {
      padding: 5px 0;
      font-size: 14px;
      line-height: 26px;

      .new-tag {
        margin-left: 5px;
        padding: 0 7px;
      }
    }
  }
}
