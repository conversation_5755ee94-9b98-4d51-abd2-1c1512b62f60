.bread-crumb-nav {
  z-index: 10;
  padding: 33px 0 3px;

  .breadcrumb {
    flex-wrap: nowrap;
    margin: 0;
  }

  .breadcrumb-item {
    color: #606266;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;

    &.active {
      color: #5a65ea;
      font-weight: 500;
      font-family: Poppins;
    }

    &:last-child {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    + .breadcrumb-item {
      padding-left: 4px;

      &::before {
        position: relative;
        top: 1px;
        float: unset;
        padding-right: 4px;
        content: url('../../../assets/images/chatdoc/separator.svg');
      }
    }

    a {
      display: inline-block;
      color: @text-color;

      &:hover {
        color: #5a65ea;
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .bread-crumb-nav {
    padding: 21px 0 1px;
  }
}
