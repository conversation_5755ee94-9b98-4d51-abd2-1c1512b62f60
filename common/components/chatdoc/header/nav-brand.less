.nav-brand {
  display: flex;
  align-items: center;

  .brand-logo {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
  }

  .brand-name {
    margin-left: 10px;
    color: @dark-title-color;
    font-weight: 500;
    font-size: 20px;
    font-family: <PERSON><PERSON><PERSON>;
    line-height: 22px;
  }

  .cross-icon {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    margin: 0 20px;
  }
}

@media (max-width: @mini-screen-size) {
  .nav-brand {
    .brand-logo {
      width: 35px;
      height: 35px;
    }

    .brand-name {
      margin-left: 7px;
      font-size: 14px;
      line-height: 16px;
    }

    .cross-icon {
      width: 8px;
      height: 8px;
      margin: 0 8px;
    }
  }
}

@media (max-width: @least-screen-size) {
  .nav-brand {
    &.nav-brand-cooperate {
      position: absolute;
      left: -15px;
      transform: scale(0.72);
    }
  }
}
