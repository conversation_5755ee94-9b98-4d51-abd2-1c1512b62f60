.browser-dropdown {
  .dropdown-menu {
    position: absolute;
    margin-top: 0;
    padding-top: 15px;
    background-color: transparent;
    border: none;
  }

  .dropdown-toggle {
    display: flex;
    align-items: center;
    height: 46px;

    &.nav-link {
      margin-right: 10px;
      padding: 0 20px !important;
      background:
        linear-gradient(#000, #000) padding-box,
        linear-gradient(
            90deg,
            rgba(255, 255, 255, 20%),
            rgba(255, 255, 255, 50%),
            rgba(255, 255, 255, 20%)
          )
          border-box;
      border: 1px solid transparent;
      border-radius: 100px;
    }

    img {
      width: 30px;
    }

    &::after {
      content: unset;
    }
  }

  &:hover {
    .dropdown-menu {
      display: block;
    }
  }

  .menu-content {
    display: flex;
    flex-direction: column;
    padding: 30px;
    background-color: #fff;
    border: 1px solid #dedede;
    border-radius: 4px;
    box-shadow: 0 20px 38px 0 rgba(101, 122, 147, 20%);
    row-gap: 30px;
  }

  .menu-item {
    color: @dark-title-color;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 26px;
    white-space: nowrap;
    cursor: pointer;

    .extension-icon {
      margin-right: 5px;
    }

    &:hover {
      color: #6576db;
    }
  }
}
