.api-dropdown {
  .dropdown-menu {
    position: absolute;
    margin-top: 0;
    padding-top: 15px;
    background-color: transparent;
    border: none;
  }

  .dropdown-toggle {
    &::after {
      content: unset;
    }

    &.nav-link {
      margin-right: 0;
    }
  }

  &:hover {
    .dropdown-menu {
      display: block;
    }
  }

  .menu-content {
    display: flex;
    flex-direction: column;
    padding: 30px;
    background-color: #fff;
    border: 1px solid #dedede;
    border-radius: 4px;
    box-shadow: 0 20px 38px 0 rgba(101, 122, 147, 20%);
    row-gap: 30px;
  }

  .menu-item {
    color: @dark-title-color;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 26px;
    white-space: nowrap;
    cursor: pointer;

    &:hover {
      color: #6576db;
    }
  }
}

@media (max-width: @large-screen-size) {
  .api-dropdown {
    .dropdown-menu {
      padding-top: 8px;
    }

    .menu-content {
      padding: 20px;
      row-gap: 20px;
    }

    .menu-item {
      font-size: 16px;
      line-height: 20px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .api-dropdown {
    .dropdown-toggle {
      display: none;
    }

    .dropdown-menu {
      position: relative !important;
      display: block;
      padding: 0;
    }

    .menu-content {
      padding: 0;
      border: none;
      box-shadow: none;
      row-gap: unset;
    }

    .menu-item {
      padding: 12px 0;
      font-size: 15px;
      line-height: 24px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .api-dropdown {
    .menu-item {
      padding: 10px 20px;
      font-size: 14px;
    }
  }
}
