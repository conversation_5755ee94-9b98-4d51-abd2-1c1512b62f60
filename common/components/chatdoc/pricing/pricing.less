.pricing-wrapper {
  position: relative;
  z-index: 1;
  background-color: #fff;
  border-bottom: @border-base;

  &.chatdoc-v3-pricing {
    background-color: #000;
    border-bottom: none;

    .pricing-title {
      font-size: 68px;
      line-height: 80px;
      background: linear-gradient(88deg, #FFF 2.84%, rgba(255, 255, 255, 0.80) 76.02%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .pricing-subtitle {
      color: rgba(255, 255, 255, 60%);
    }

    .switch-time {
      color: rgba(255, 255, 255, 80%);
    }

    .pricing-highlight {
      color: #627ae6;
    }

    .desc-item {
      color: #fff;
    }

    .title-text-alias,
    .price-value {
      color: #fff;
    }

    .pricing-card-pro {
      position: relative;
      background: linear-gradient(130deg, #000 1%, #222 114.3%);
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 16px;
      box-shadow: none;

      &::before {
        position: absolute;
        top: -1px;
        left: -1px;
        z-index: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #4bb75a 0%, transparent 20%) top
          left / 200px 200px no-repeat;
        border-radius: 18px;
        content: '';
      }

      &::after {
        position: absolute;
        top: -23px;
        left: -33px;
        z-index: 0;
        width: 140px;
        height: 117px;
        background: radial-gradient(
          ellipse at center,
          rgba(75, 183, 90, 30%) 60%,
          transparent 100%
        );
        filter: blur(70px);
        content: '';
      }
    }

    .pricing-card-pro {
      padding: 0;

      .pricing-card-item-container {
        position: relative;
        z-index: 1;
        padding: 0 30px;
        background: linear-gradient(130deg, #000 1%, #222 114.3%);
        border-radius: 16px;

        &::before {
          position: absolute;
          right: 0;
          bottom: 260px;
          z-index: 1;
          width: 1px;
          height: 117px;
          background: linear-gradient(
            to bottom,
            rgba(98, 122, 230, 0%) 0%,
            #627ae6 50%,
            rgba(98, 122, 230, 0%) 100%
          );
          box-shadow: 30px 0 80px 20px rgba(12, 55, 248, 30%);
          content: '';
        }
      }
    }
  }

  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 120px 0 90px;
  }

  .pricing-subtitle {
    margin-bottom: 30px;
  }

  .pricing-highlight {
    color: #6576db;
  }

  .pricing-switch {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
  }

  .switch-time {
    font-size: 20px;
    line-height: 36px;
    white-space: nowrap;
  }

  .switch-time {
    font-weight: 500;
    font-family: Poppins;
  }

  .switch-check {
    display: inline-block;
    margin: 0 20px;
    padding: 0;
    border: 1px solid #6b6c6f;
    border-radius: 99px;

    .form-check-input {
      width: 62px;
      height: 34px;
      margin: 0;
      padding: 4px;
      background: url('../../../assets/images/chatdoc/input-switch-circle.svg')
        no-repeat;
      background-position: left center;
      border: none;
      box-shadow: none;
      cursor: pointer;
      transition: background-position 0.15s ease-in-out;

      &:checked {
        background-position: right center;
      }
    }
  }

  .pricing-cards-container {
    width: 84%;
  }

  .pricing-cards {
    display: flex;
  }

  .pricing-card-item {
    display: flex;
    flex-direction: column;
    width: 16%;
    background-color: transparent;
    border: none;
  }

  .pricing-card-item-container {
    width: 100%;
    height: 100%;
  }

  .pricing-card-free,
  .pricing-card-pro {
    align-items: center;

    .desc-item {
      justify-content: center;
      font-weight: 400;
      font-family: Poppins;
      text-align: center;

      &:last-child {
        white-space: pre-wrap;
      }
    }
  }

  .pricing-card-free {
    width: 50%;

    .card-btn {
      color: #6576db;
      background-color: transparent;
    }
  }

  .pricing-card-pro {
    width: 34%;
    margin-left: 30px;
    padding: 0 30px;
    border: 1px solid #6576db;
    border-radius: 40px;
    box-shadow: 0 20px 38px rgba(76, 101, 251, 20%);

    .card-btn {
      color: #fff;
    }
  }

  .card-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 25%;
    margin: 0;
    padding: 30px 0 40px;
  }

  .title-text {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
  }

  .title-text-alias {
    color: @dark-color;
    font-weight: 500;
    font-size: 24px;
    font-family: Poppins;
    line-height: 36px;
  }

  .title-icon {
    flex-shrink: 0;
    width: 36px;
  }

  .title-price {
    display: flex;
    align-items: flex-end;
    margin-bottom: 10px;
  }

  .price-value {
    color: @dark-color;
    font-weight: 500;
    font-size: 30px;
    font-family: Poppins;
    line-height: 45px;
  }

  .price-time {
    color: #9ea0a5;
    font-size: 24px;
  }

  .card-btn {
    padding: 9px 29px;
    font-size: 16px;
    line-height: 22px;
    background-color: #6576db;
    border: 1px solid #6576db;
  }

  .card-desc {
    width: 100%;
    padding-bottom: 30px;
  }

  .desc-item {
    display: flex;
    height: 66px;
    padding: 20px 0;
    color: @dark-color;
    font-weight: 500;
    font-size: 18px;
    font-family: Poppins;
    line-height: 27px;
    white-space: nowrap;
    border-bottom: @border-base;

    &:last-child {
      border-bottom: none;
    }
  }

  .right-icon {
    width: 19.5px;
    height: 13.5px;
  }
}

@media (max-width: @large-screen-size) {
  .pricing-wrapper {
    .pricing-card-item {
      width: 20%;
    }

    .pricing-card-free,
    .pricing-card-pro {
      width: 40%;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .pricing-wrapper {
    .pricing-cards-container {
      width: 90%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .pricing-wrapper {
    .container {
      padding: 90px 0 70px;
    }

    .pricing-switch {
      margin-bottom: 50px;
    }

    .pricing-cards-container {
      width: 100%;
    }

    .card-title {
      padding: 20px 0;
    }

    .pricing-card-pro {
      margin-left: 20px;
      padding: 0 20px;
    }
  }

  .pricing-wrapper.chatdoc-v3-pricing {
    margin-top: 129px;

    .pricing-title {
      margin-bottom: 54px;
      padding: 0;
      font-size: 40px;
      line-height: 50px;
      letter-spacing: normal;
    }

    .pricing-subtitle {
      margin-bottom: 47px;
      font-size: 20px;
      line-height: 28px;
    }

    .pricing-switch {
      margin-bottom: 58px;
    }

    .switch-time {
      font-size: 20px;
    }

    .switch-check {
      .form-check-input {
        width: 80px;
        height: 34px;
      }
    }

    .pricing-cards-container {
      padding: 0;

      .pricing-cards {
        flex-direction: column;
      }

      .card:first-child {
        display: none;
      }
    }

    .pricing-card-free {
      width: 100%;
      border: none;
    }

    .pricing-card-pro {
      width: 100%;
      margin-top: 60px;

      .pricing-card-item-container {
        padding: 60px 45px 43px;

        .price-value {
          margin: 0;
        }

        .title-price {
          margin-bottom: 27px;
        }

        .card-btn {
          padding: 12px 24px;
        }
      }
    }

    .card-title {
      padding-bottom: 45px;
    }

    .card-desc {
      border-top: none;
    }

    .desc-item {
      font-size: 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 10%);
      stroke-width: 1px;

      &:last-child {
        height: auto;
        padding-top: 25px;
        line-height: 24px;
        border-bottom: none;
      }
    }

    .title-text {
      margin-bottom: 20px;
    }

    .title-text-alias {
      font-size: 20px;
      line-height: 28px;
    }

    .price-value {
      margin-bottom: 27px;
      font-size: 30px;
      line-height: 30px;
    }

    .card-btn {
      width: auto;
      padding: 12px 31px;
      font-size: 16px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .pricing-wrapper {
    .container {
      padding: 60px 0 50px;
    }

    .pricing-title {
      padding: 0 20px;
    }

    .pricing-subtitle {
      margin-bottom: 10px;
    }

    .pricing-switch {
      margin-bottom: 30px;
    }

    .switch-check {
      margin: 0 13px;

      .form-check-input {
        width: 45px;
        height: 27px;
        padding: 3px;
      }
    }

    .switch-time {
      font-size: 14px;
      line-height: 25px;
    }

    .pricing-cards-container {
      padding: 0 20px;
    }

    .pricing-card-item {
      width: 27%;
      border-right: @border-base;
      border-radius: unset;
    }

    .pricing-card-free {
      width: 30%;
    }

    .pricing-card-pro {
      width: 43%;
      margin: 0;
      padding: 0;
      border: none;
      box-shadow: none;
    }

    .pricing-card-free,
    .pricing-card-pro {
      .desc-item {
        padding: 0 5px;

        &:last-child {
          white-space: unset;
        }
      }
    }

    .card-title {
      justify-content: space-between;
      padding-top: 0;
    }

    .title-text {
      margin-bottom: 6px;
    }

    .title-text-alias {
      font-size: 14px;
      line-height: 21px;
    }

    .title-icon {
      width: 21px;
    }

    .title-price {
      margin-bottom: 6px;
      white-space: nowrap;
    }

    .price-value {
      font-size: 17px;
      line-height: 26px;
    }

    .price-time {
      font-size: 14px;
    }

    .card-btn {
      width: 86px;
      padding: 6px 0;
      font-size: 10px;
      line-height: 13px;
    }

    .card-desc {
      padding-bottom: 0;
      border-top: @border-base;
    }

    .desc-item {
      align-items: center;
      height: 62px;
      padding: 0 4px 0 0;
      font-size: 14px;
      line-height: 21px;
      white-space: unset;
    }

    .right-icon {
      width: 13px;
      height: 9px;
    }
  }
}

@media (max-width: 576px) {
  .pricing-wrapper {
    .desc-item {
      &:last-child {
        height: 80px;
      }
    }
  }
}
