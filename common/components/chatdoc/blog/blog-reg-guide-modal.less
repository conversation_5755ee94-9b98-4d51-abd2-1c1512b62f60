.blog-reg-guide-modal {
  --modal-logo-size: 120px;

  font-weight: 400;
  font-family: Poppins;

  .modal-dialog {
    max-width: 630px;
  }

  .modal-content {
    position: relative;
    z-index: 0;
    border: none;
    border-radius: 20px;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(
        91deg,
        #ff8371 -0.39%,
        #e37cf3 61.5%,
        #7291ff 100%
      );
      border-radius: 20px;
      content: '';
    }
  }

  .blog-reg-guide-modal-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 20px;
  }

  .modal-header {
    border-bottom: none;
    border-radius: 20px;
  }

  .btn-close {
    position: relative;
    z-index: 1;
    width: 36px;
    height: 36px;
    margin: 0;
    margin-left: auto;
    padding: 0;
    background: url('../../../assets/images/chatdoc/button-close.png') no-repeat;
    background-size: 100%;
    opacity: unset;

    &:focus {
      box-shadow: none;
    }
  }

  .modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 100px;
    padding: 0 20px 20px;
  }

  .modal-logo {
    position: absolute;
    top: calc(-1 * (var(--modal-logo-size) / 2));
    flex-shrink: 0;
    width: var(--modal-logo-size);
    height: var(--modal-logo-size);
  }

  .modal-card {
    padding: 94px 20px 42px;
    text-align: center;
    background-color: #fff;
    border-radius: 16px;
  }

  .modal-title {
    margin-bottom: 24px;
    color: #303133;
    font-weight: 600;
    font-size: 32px;
    font-family: Poppins;
    line-height: 36px;
  }

  .modal-desc {
    margin-bottom: 18px;
    color: #303133;
    font-size: 21px;
    line-height: 32px;
  }

  .modal-button {
    padding: 15px 31px;
    font-weight: 500;
    font-size: 32px;
    font-family: Poppins;
    line-height: 32px;
    border-radius: 8px;
  }
}

@media (max-width: @large-screen-size) {
  .blog-reg-guide-modal {
    --modal-logo-size: 80px;

    .modal-dialog {
      max-width: 540px;
    }

    .modal-body {
      margin-top: 50px;
    }

    .btn-close {
      width: 28px;
      height: 28px;
    }

    .modal-card {
      padding: 70px 15px 30px;
    }

    .modal-title {
      font-size: 30px;
      line-height: 32px;
    }

    .modal-desc {
      font-size: 18px;
      line-height: 28px;
    }

    .modal-button {
      padding: 9px 29px;
      font-size: 23px;
      border-radius: 6px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .blog-reg-guide-modal {
    --modal-logo-size: 64px;

    .modal-header {
      padding: 12px;
    }

    .btn-close {
      width: 20px;
      height: 20px;
    }

    .modal-body {
      margin-top: 42px;
      padding: 0 10px 10px;
    }

    .modal-card {
      padding: 44px 20px 16px;
    }

    .modal-title {
      margin-bottom: 8px;
      font-size: 17px;
      line-height: 24px;
    }

    .modal-desc {
      margin-bottom: 15px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }

    .modal-button {
      padding: 8px 12px;
      font-weight: 400;
      font-size: 14px;
      line-height: normal;
    }
  }
}
