import React, { useState, useCallback, useMemo, useEffect } from 'react';
import LazyLoad, { forceCheck } from 'react-lazyload';
import { GatsbyImage } from 'gatsby-plugin-image';
import { Container, Nav, NavItem } from 'react-bootstrap';
import ProjectButton from '../button/project-button';
import XGPlayerVideo from '../../video/xgplayer-video/xgplayer-video';
import { useCheckScreenSize } from '../../../hooks/useCheckScreenSizeHook';
import { useHandleResize } from '../../../hooks/useHandleResizeHook';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import FontFaceObserver from 'fontfaceobserver';
import classnames from 'classnames';
import './scenarios.less';

const ScenarioVideo = ({ videoData }) => {
  return (
    <XGPlayerVideo
      className="content-video"
      src={videoData.video.publicURL}
      poster={videoData.poster}
    />
  );
};

const Scenarios = () => {
  const { scenariosData } = useGlobalContext();
  const { isMiniScreenSize } = useCheckScreenSize();
  const [navActived, setNavActived] = useState(0);
  const [navItemWidth, setNavItemWidth] = useState([]);
  const [navItemHeight, setNavItemHeight] = useState([]);

  const getNavItemSize = useCallback(() => {
    const navItemList = Array.from(
      document.querySelectorAll('.scenarios-nav>.nav-item'),
    );
    setNavItemWidth(
      navItemList.map((el) => parseFloat(getComputedStyle(el).width)),
    );
    setNavItemHeight(
      navItemList.map((el) => parseFloat(getComputedStyle(el).height)),
    );
  }, []);

  const navActivedStyle = useMemo(() => {
    if (isMiniScreenSize) {
      return {
        height: navItemHeight[navActived] + 'px',
        top:
          navItemHeight
            .slice(0, navActived)
            .reduce((sum, height) => sum + height, 0) + 'px',
      };
    } else {
      return {
        width: navItemWidth[navActived] + 'px',
        left:
          navItemWidth
            .slice(0, navActived)
            .reduce((sum, width) => sum + width, 0) + 'px',
      };
    }
  }, [navActived, navItemWidth, navItemHeight, isMiniScreenSize]);

  const handleChangeScenario = (index) => {
    setNavActived(index);
    setTimeout(() => {
      forceCheck();
    }, 0);
  };

  useHandleResize(() => {
    getNavItemSize();
  });

  useEffect(() => {
    const navItemfont = new FontFaceObserver('Poppins');
    navItemfont.load().then(() => {
      getNavItemSize();
    });
  }, [getNavItemSize]);

  return (
    <div className="scenarios-wrapper">
      <Container>
        <h3 className="common-title scenarios-title">
          Handle Various Scenarios, Work & Study Smarter
        </h3>
        <div>
          <div className="scenarios-nav-container">
            <Nav className="scenarios-nav">
              <div className="nav-bg" style={navActivedStyle} />
              {scenariosData.map((item, index) => (
                <NavItem
                  key={index}
                  className={classnames({
                    'nav-item': true,
                    active: index === navActived,
                  })}
                  onClick={() => handleChangeScenario(index)}>
                  {item.name}
                </NavItem>
              ))}
            </Nav>
          </div>
          <div className="scenarios-content-container">
            {scenariosData.map((item, index) => (
              <div
                key={index}
                className={classnames({
                  'content-item': true,
                  active: index === navActived,
                })}>
                <div className="content-left">
                  <GatsbyImage
                    image={item.icon.childImageSharp.gatsbyImageData}
                    alt={item.icon.name}
                    className="content-icon"
                  />
                  <h3 className="content-title">{item.title}</h3>
                  <p className="content-desc">{item.desc}</p>
                  <ProjectButton className="content-btn" text="Try for Free" />
                </div>
                <div className="content-right">
                  {index === 0 ? (
                    <ScenarioVideo videoData={item} />
                  ) : (
                    <LazyLoad>
                      <ScenarioVideo videoData={item} />
                    </LazyLoad>
                  )}
                </div>
                <div className="content-bottom">
                  <ProjectButton className="content-btn" text="Try for Free" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Scenarios;
