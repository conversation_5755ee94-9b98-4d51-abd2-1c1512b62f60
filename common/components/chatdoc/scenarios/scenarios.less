.scenarios-wrapper {
  padding: 120px 0;

  .scenarios-title {
    margin-bottom: 32px;
  }

  .scenarios-nav-container {
    margin-bottom: 90px;
  }

  .scenarios-nav {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
    width: fit-content;
    margin: 0 auto;
    background-color: #f6f6f7;
    border: 1px solid #e2e5ed;
    border-radius: 20px;
    box-shadow: inset 0 4px 4px rgba(102, 113, 123, 21%);
  }

  .nav-bg {
    position: absolute;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #fdfeff -2.31%, #ecf0f5 100.71%);
    border-radius: 20px;
    box-shadow: 0 25.29553px 50.59106px 0 rgba(101, 122, 147, 27%);
    transition: left 0.5s ease;
  }

  .nav-item {
    z-index: 3;
    padding: 18px 30px 16px;
    color: @dark-title-color;
    font-weight: 500;
    font-size: 20px;
    font-family: Poppins;
    line-height: 30px;
    cursor: pointer;

    &.active {
      color: #6576db;
    }
  }

  .content-item {
    display: none;
    justify-content: space-between;

    &.active {
      display: flex;
    }
  }

  .content-left {
    width: 35%;
    max-width: 460px;
  }

  .content-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 24px;
  }

  .content-title {
    margin-bottom: 31px;
    color: @dark-title-color;
    font-weight: 500;
    font-size: 30px;
    font-family: Poppins;
    line-height: 45px;
    letter-spacing: 2px;
    white-space: pre;
  }

  .content-desc {
    margin-bottom: 60px;
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
    text-shadow: 0 10px 35px rgba(53, 16, 150, 10%);
  }

  .content-right {
    width: 63%;
    max-width: 912px;

    .lazyload-wrapper {
      width: 100%;
    }
  }

  .content-bottom {
    display: none;
  }
}

@media (max-width: 1400px) {
  .scenarios-wrapper {
    .content-right {
      width: 62%;
    }

    .content-title {
      font-size: 28px;
      line-height: 42px;
      letter-spacing: 1.5px;
    }

    .content-desc {
      margin-bottom: 40px;
      font-size: 18px;
      line-height: 30px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .scenarios-wrapper {
    .content-icon {
      width: 30px;
      height: 30px;
      margin-bottom: 16px;
    }

    .content-title {
      margin-bottom: 25px;
      font-size: 24px;
      line-height: 38px;
      letter-spacing: 1px;
    }

    .content-desc {
      margin-bottom: 35px;
      font-size: 17px;
      line-height: 28px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .scenarios-wrapper {
    padding: 90px 0;

    .content-right,
    .content-left {
      width: 100%;
      max-width: 100%;
    }

    .scenarios-nav-container {
      margin-bottom: 60px;
    }

    .nav-item {
      padding: 16px 13px 15px;
      font-size: 18px;
      line-height: 26px;
    }

    .content-item {
      flex-direction: column;
    }

    .content-title {
      margin-bottom: 20px;
      white-space: unset;
    }

    .content-desc,
    .content-btn {
      margin-bottom: 15px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .scenarios-wrapper {
    padding: 60px 0;

    .scenarios-title {
      margin-bottom: 21px;
      letter-spacing: 0.545px;
    }

    .scenarios-nav-container {
      margin-bottom: 30px;
    }

    .scenarios-nav {
      flex-direction: column;
      align-items: center;
      width: 100%;
      border-radius: 11px;
    }

    .nav-bg {
      border-radius: 11px;
      transition: top 0.3s ease;
    }

    .nav-item {
      width: 100%;
      padding-top: 9px;
      padding-bottom: 9px;
      font-size: 14px;
      line-height: 18px;
      text-align: center;
    }

    .content-icon {
      width: 20px;
      height: 20px;
    }

    .content-title {
      margin-bottom: 10px;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      white-space: pre;
    }

    .content-desc {
      margin-bottom: 20px;
      font-size: 14px;
      line-height: 24px;
    }

    .content-right {
      margin-bottom: 35px;
    }

    .content-left {
      .content-btn {
        display: none;
      }
    }

    .content-bottom {
      display: flex;
      justify-content: center;

      .content-btn {
        margin-bottom: 0;
      }
    }
  }
}
