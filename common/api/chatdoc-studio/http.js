import axios from 'axios';
import get from 'lodash/get';
import Crypt from 'cryptjs';
import { dataEncrypt, fromXBinaryKey } from './encrypt';

export const baseURL = `/chatdoc-studio/api/v1`;

let xBinaryKey = '';

export const handleResponseData = (data) => {
  if (xBinaryKey) {
    const packageEncrypt = fromXBinaryKey(xBinaryKey);
    return packageEncrypt.decryptJson(new Uint8Array(data));
  } else {
    let encryptData = Crypt.fromBytes(new Uint8Array(data));
    try {
      encryptData = JSON.parse(encryptData);
    } catch (e) {
      return Promise.reject(e);
    }

    return encryptData;
  }
};

const timeout = 120e3;

const http = axios.create({
  baseURL,
  timeout,
  responseType: 'arraybuffer',
});

http.interceptors.request.use(
  (config) => {
    if (config.data && !(config.data instanceof FormData)) {
      const { data } = dataEncrypt(config.data);

      if (!config.headers) {
        config.headers = {};
      }
      config.headers['content-type'] = 'application/json';
      config.data = data;
    }

    return config;
  },
  (err) => {
    return Promise.reject(err);
  },
);

http.interceptors.response.use(
  function resolve(result) {
    if (result.status !== 200 && result.status !== 301) {
      return Promise.reject(new Error('Failed to get data'));
    }

    if (result.headers['x-binary-key']) {
      xBinaryKey = result.headers['x-binary-key'];
    }

    const data = handleResponseData(result.data);

    // if (data.status === 'ok') {
    //   return data.data;
    // }
    return data;
  },
  function reject(error) {
    const { response, message } = error;
    if (error.name === 'CanceledError') {
      return Promise.reject({ type: 'cancel', message });
    }
    if (error.message.includes('timeout')) {
      return Promise.reject({ type: 'http', message: 'request timeout' });
    }
    if (!response) {
      return Promise.reject({ type: 'http', message: 'unknown error' });
    }

    if (response.data && response.data.byteLength > 0) {
      let errorText = message;
      try {
        errorText = new TextDecoder().decode(response.data);
        response.data = JSON.parse(errorText);
      } catch {
        response.data = { detail: errorText || 'parse error' };
      }
    }

    const baseErrorData = {
      type: 'http',
      status: response.status,
      data: response.data || null,
    };

    const errorMessage = get(response, 'data.detail');

    const errorInfo = get(response, 'data.data');

    switch (response.status) {
      case 401: {
        return Promise.reject({
          ...baseErrorData,
          message: 'authorization expires, please sign in again',
          info: errorInfo || {},
        });
      }
      case 403: {
        if (errorInfo?.reason === 'banned') {
          const urlList = ['/users/sign-up/apply'];
          let needRedirect = !urlList.includes(response.config.url);
          return Promise.reject({
            type: 'banned',
            status: response.status,
          });
        }
        return Promise.reject({
          ...baseErrorData,
          message: errorMessage || 'no operation permission',
          info: errorInfo || {},
        });
      }
      case 404:
        return Promise.reject({
          ...baseErrorData,
          message: errorMessage || 'not found',
          info: errorInfo || {},
        });
      case 400:
      case 422:
        return Promise.reject({
          ...baseErrorData,
          message: errorMessage || 'request error',
          info: errorInfo || {},
        });
      case 500:
      case 502:
        return Promise.reject({
          ...baseErrorData,
          message: 'service error',
          info: errorInfo || {},
        });
      default:
        return Promise.reject({
          ...baseErrorData,
          message: errorMessage || 'unknown error',
          info: errorInfo || {},
        });
    }
  },
);

export default http;
