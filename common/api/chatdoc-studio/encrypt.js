import Crypt from 'cryptjs';
import base64js from 'base64-js';
import { getEnvVariables } from '../../utils/env';

const { secretKey } = getEnvVariables();

let binaryKey = '';

export function fromXBinaryKey(base64BinaryKey) {
  const encryptBinaryKey = base64js.toByteArray(base64BinaryKey);
  const keyEncrypt = new Crypt(secretKey);
  binaryKey = keyEncrypt.decrypt(encryptBinaryKey);
  return new Crypt(binaryKey);
}

export function dataEncrypt(data) {
  if (binaryKey) {
    const secretKey = new Crypt(binaryKey);
    data = secretKey.encryptJson(data);
  }

  return {
    key: binaryKey,
    data,
  };
}
