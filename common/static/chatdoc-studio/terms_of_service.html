<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Terms of Service</title>
    <style>
        body {
            padding: 20px;
        }
        /* light */
        .markdown-body {
            color-scheme: light;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            margin: 0;
            color: #1f2328;
            background-color: #ffffff;
            font-family: -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji";
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .markdown-body .octicon {
            display: inline-block;
            fill: currentColor;
            vertical-align: text-bottom;
        }

        .markdown-body h1:hover .anchor .octicon-link:before,
        .markdown-body h2:hover .anchor .octicon-link:before,
        .markdown-body h3:hover .anchor .octicon-link:before,
        .markdown-body h4:hover .anchor .octicon-link:before,
        .markdown-body h5:hover .anchor .octicon-link:before,
        .markdown-body h6:hover .anchor .octicon-link:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
        }

        .markdown-body details,
        .markdown-body figcaption,
        .markdown-body figure {
            display: block;
        }

        .markdown-body summary {
            display: list-item;
        }

        .markdown-body [hidden] {
            display: none !important;
        }

        .markdown-body a {
            background-color: transparent;
            color: #0969da;
            text-decoration: none;
        }

        .markdown-body abbr[title] {
            border-bottom: none;
            -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted;
        }

        .markdown-body b,
        .markdown-body strong {
            font-weight: 600;
        }

        .markdown-body dfn {
            font-style: italic;
        }

        .markdown-body h1 {
            margin: .67em 0;
            font-weight: 600;
            padding-bottom: .3em;
            font-size: 2em;
            border-bottom: 1px solid #d1d9e0b3;
        }

        .markdown-body mark {
            background-color: #fff8c5;
            color: #1f2328;
        }

        .markdown-body small {
            font-size: 90%;
        }

        .markdown-body sub,
        .markdown-body sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline;
        }

        .markdown-body sub {
            bottom: -0.25em;
        }

        .markdown-body sup {
            top: -0.5em;
        }

        .markdown-body img {
            border-style: none;
            max-width: 100%;
            box-sizing: content-box;
        }

        .markdown-body code,
        .markdown-body kbd,
        .markdown-body pre,
        .markdown-body samp {
            font-family: monospace;
            font-size: 1em;
        }

        .markdown-body figure {
            margin: 1em 2.5rem;
        }

        .markdown-body hr {
            box-sizing: content-box;
            overflow: hidden;
            background: transparent;
            border-bottom: 1px solid #d1d9e0b3;
            height: .25em;
            padding: 0;
            margin: 1.5rem 0;
            background-color: #d1d9e0;
            border: 0;
        }

        .markdown-body input {
            font: inherit;
            margin: 0;
            overflow: visible;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
        }

        .markdown-body [type=button],
        .markdown-body [type=reset],
        .markdown-body [type=submit] {
            -webkit-appearance: button;
            appearance: button;
        }

        .markdown-body [type=checkbox],
        .markdown-body [type=radio] {
            box-sizing: border-box;
            padding: 0;
        }

        .markdown-body [type=number]::-webkit-inner-spin-button,
        .markdown-body [type=number]::-webkit-outer-spin-button {
            height: auto;
        }

        .markdown-body [type=search]::-webkit-search-cancel-button,
        .markdown-body [type=search]::-webkit-search-decoration {
            -webkit-appearance: none;
            appearance: none;
        }

        .markdown-body ::-webkit-input-placeholder {
            color: inherit;
            opacity: .54;
        }

        .markdown-body ::-webkit-file-upload-button {
            -webkit-appearance: button;
            appearance: button;
            font: inherit;
        }

        .markdown-body a:hover {
            text-decoration: underline;
        }

        .markdown-body ::placeholder {
            color: #59636e;
            opacity: 1;
        }

        .markdown-body hr::before {
            display: table;
            content: "";
        }

        .markdown-body hr::after {
            display: table;
            clear: both;
            content: "";
        }

        .markdown-body table {
            border-spacing: 0;
            border-collapse: collapse;
            display: block;
            width: max-content;
            max-width: 100%;
            overflow: auto;
            font-variant: tabular-nums;
        }

        .markdown-body td,
        .markdown-body th {
            padding: 0;
        }

        .markdown-body details summary {
            cursor: pointer;
        }

        .markdown-body a:focus,
        .markdown-body [role=button]:focus,
        .markdown-body input[type=radio]:focus,
        .markdown-body input[type=checkbox]:focus {
            outline: 2px solid #0969da;
            outline-offset: -2px;
            box-shadow: none;
        }

        .markdown-body a:focus:not(:focus-visible),
        .markdown-body [role=button]:focus:not(:focus-visible),
        .markdown-body input[type=radio]:focus:not(:focus-visible),
        .markdown-body input[type=checkbox]:focus:not(:focus-visible) {
            outline: solid 1px transparent;
        }

        .markdown-body a:focus-visible,
        .markdown-body [role=button]:focus-visible,
        .markdown-body input[type=radio]:focus-visible,
        .markdown-body input[type=checkbox]:focus-visible {
            outline: 2px solid #0969da;
            outline-offset: -2px;
            box-shadow: none;
        }

        .markdown-body a:not([class]):focus,
        .markdown-body a:not([class]):focus-visible,
        .markdown-body input[type=radio]:focus,
        .markdown-body input[type=radio]:focus-visible,
        .markdown-body input[type=checkbox]:focus,
        .markdown-body input[type=checkbox]:focus-visible {
            outline-offset: 0;
        }

        .markdown-body kbd {
            display: inline-block;
            padding: 0.25rem;
            font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            line-height: 10px;
            color: #1f2328;
            vertical-align: middle;
            background-color: #f6f8fa;
            border: solid 1px #d1d9e0b3;
            border-bottom-color: #d1d9e0b3;
            border-radius: 6px;
            box-shadow: inset 0 -1px 0 #d1d9e0b3;
        }

        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-body h2 {
            font-weight: 600;
            padding-bottom: .3em;
            font-size: 1.5em;
            border-bottom: 1px solid #d1d9e0b3;
        }

        .markdown-body h3 {
            font-weight: 600;
            font-size: 1.25em;
        }

        .markdown-body h4 {
            font-weight: 600;
            font-size: 1em;
        }

        .markdown-body h5 {
            font-weight: 600;
            font-size: .875em;
        }

        .markdown-body h6 {
            font-weight: 600;
            font-size: .85em;
            color: #59636e;
        }

        .markdown-body p {
            margin-top: 0;
            margin-bottom: 10px;
        }

        .markdown-body blockquote {
            margin: 0;
            padding: 0 1em;
            color: #59636e;
            border-left: .25em solid #d1d9e0;
        }

        .markdown-body ul,
        .markdown-body ol {
            margin-top: 0;
            margin-bottom: 0;
            padding-left: 2em;
        }

        .markdown-body ol ol,
        .markdown-body ul ol {
            list-style-type: lower-roman;
        }

        .markdown-body ul ul ol,
        .markdown-body ul ol ol,
        .markdown-body ol ul ol,
        .markdown-body ol ol ol {
            list-style-type: lower-alpha;
        }

        .markdown-body dd {
            margin-left: 0;
        }

        .markdown-body tt,
        .markdown-body code,
        .markdown-body samp {
            font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            font-size: 12px;
        }

        .markdown-body pre {
            margin-top: 0;
            margin-bottom: 0;
            font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            font-size: 12px;
            word-wrap: normal;
        }

        .markdown-body .octicon {
            display: inline-block;
            overflow: visible !important;
            vertical-align: text-bottom;
            fill: currentColor;
        }

        .markdown-body input::-webkit-outer-spin-button,
        .markdown-body input::-webkit-inner-spin-button {
            margin: 0;
            appearance: none;
        }

        .markdown-body .mr-2 {
            margin-right: 0.5rem !important;
        }

        .markdown-body::before {
            display: table;
            content: "";
        }

        .markdown-body::after {
            display: table;
            clear: both;
            content: "";
        }

        .markdown-body>*:first-child {
            margin-top: 0 !important;
        }

        .markdown-body>*:last-child {
            margin-bottom: 0 !important;
        }

        .markdown-body a:not([href]) {
            color: inherit;
            text-decoration: none;
        }

        .markdown-body .absent {
            color: #d1242f;
        }

        .markdown-body .anchor {
            float: left;
            padding-right: 0.25rem;
            margin-left: -20px;
            line-height: 1;
        }

        .markdown-body .anchor:focus {
            outline: none;
        }

        .markdown-body p,
        .markdown-body blockquote,
        .markdown-body ul,
        .markdown-body ol,
        .markdown-body dl,
        .markdown-body table,
        .markdown-body pre,
        .markdown-body details {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .markdown-body blockquote>:first-child {
            margin-top: 0;
        }

        .markdown-body blockquote>:last-child {
            margin-bottom: 0;
        }

        .markdown-body h1 .octicon-link,
        .markdown-body h2 .octicon-link,
        .markdown-body h3 .octicon-link,
        .markdown-body h4 .octicon-link,
        .markdown-body h5 .octicon-link,
        .markdown-body h6 .octicon-link {
            color: #1f2328;
            vertical-align: middle;
            visibility: hidden;
        }

        .markdown-body h1:hover .anchor,
        .markdown-body h2:hover .anchor,
        .markdown-body h3:hover .anchor,
        .markdown-body h4:hover .anchor,
        .markdown-body h5:hover .anchor,
        .markdown-body h6:hover .anchor {
            text-decoration: none;
        }

        .markdown-body h1:hover .anchor .octicon-link,
        .markdown-body h2:hover .anchor .octicon-link,
        .markdown-body h3:hover .anchor .octicon-link,
        .markdown-body h4:hover .anchor .octicon-link,
        .markdown-body h5:hover .anchor .octicon-link,
        .markdown-body h6:hover .anchor .octicon-link {
            visibility: visible;
        }

        .markdown-body h1 tt,
        .markdown-body h1 code,
        .markdown-body h2 tt,
        .markdown-body h2 code,
        .markdown-body h3 tt,
        .markdown-body h3 code,
        .markdown-body h4 tt,
        .markdown-body h4 code,
        .markdown-body h5 tt,
        .markdown-body h5 code,
        .markdown-body h6 tt,
        .markdown-body h6 code {
            padding: 0 .2em;
            font-size: inherit;
        }

        .markdown-body summary h1,
        .markdown-body summary h2,
        .markdown-body summary h3,
        .markdown-body summary h4,
        .markdown-body summary h5,
        .markdown-body summary h6 {
            display: inline-block;
        }

        .markdown-body summary h1 .anchor,
        .markdown-body summary h2 .anchor,
        .markdown-body summary h3 .anchor,
        .markdown-body summary h4 .anchor,
        .markdown-body summary h5 .anchor,
        .markdown-body summary h6 .anchor {
            margin-left: -40px;
        }

        .markdown-body summary h1,
        .markdown-body summary h2 {
            padding-bottom: 0;
            border-bottom: 0;
        }

        .markdown-body ul.no-list,
        .markdown-body ol.no-list {
            padding: 0;
            list-style-type: none;
        }

        .markdown-body ol[type="a s"] {
            list-style-type: lower-alpha;
        }

        .markdown-body ol[type="A s"] {
            list-style-type: upper-alpha;
        }

        .markdown-body ol[type="i s"] {
            list-style-type: lower-roman;
        }

        .markdown-body ol[type="I s"] {
            list-style-type: upper-roman;
        }

        .markdown-body ol[type="1"] {
            list-style-type: decimal;
        }

        .markdown-body div>ol:not([type]) {
            list-style-type: decimal;
        }

        .markdown-body ul ul,
        .markdown-body ul ol,
        .markdown-body ol ol,
        .markdown-body ol ul {
            margin-top: 0;
            margin-bottom: 0;
        }

        .markdown-body li>p {
            margin-top: 1rem;
        }

        .markdown-body li+li {
            margin-top: .25em;
        }

        .markdown-body dl {
            padding: 0;
        }

        .markdown-body dl dt {
            padding: 0;
            margin-top: 1rem;
            font-size: 1em;
            font-style: italic;
            font-weight: 600;
        }

        .markdown-body dl dd {
            padding: 0 1rem;
            margin-bottom: 1rem;
        }

        .markdown-body table th {
            font-weight: 600;
        }

        .markdown-body table th,
        .markdown-body table td {
            padding: 6px 13px;
            border: 1px solid #d1d9e0;
        }

        .markdown-body table td>:last-child {
            margin-bottom: 0;
        }

        .markdown-body table tr {
            background-color: #ffffff;
            border-top: 1px solid #d1d9e0b3;
        }

        .markdown-body table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        .markdown-body table img {
            background-color: transparent;
        }

        .markdown-body img[align=right] {
            padding-left: 20px;
        }

        .markdown-body img[align=left] {
            padding-right: 20px;
        }

        .markdown-body .emoji {
            max-width: none;
            vertical-align: text-top;
            background-color: transparent;
        }

        .markdown-body span.frame {
            display: block;
            overflow: hidden;
        }

        .markdown-body span.frame>span {
            display: block;
            float: left;
            width: auto;
            padding: 7px;
            margin: 13px 0 0;
            overflow: hidden;
            border: 1px solid #d1d9e0;
        }

        .markdown-body span.frame span img {
            display: block;
            float: left;
        }

        .markdown-body span.frame span span {
            display: block;
            padding: 5px 0 0;
            clear: both;
            color: #1f2328;
        }

        .markdown-body span.align-center {
            display: block;
            overflow: hidden;
            clear: both;
        }

        .markdown-body span.align-center>span {
            display: block;
            margin: 13px auto 0;
            overflow: hidden;
            text-align: center;
        }

        .markdown-body span.align-center span img {
            margin: 0 auto;
            text-align: center;
        }

        .markdown-body span.align-right {
            display: block;
            overflow: hidden;
            clear: both;
        }

        .markdown-body span.align-right>span {
            display: block;
            margin: 13px 0 0;
            overflow: hidden;
            text-align: right;
        }

        .markdown-body span.align-right span img {
            margin: 0;
            text-align: right;
        }

        .markdown-body span.float-left {
            display: block;
            float: left;
            margin-right: 13px;
            overflow: hidden;
        }

        .markdown-body span.float-left span {
            margin: 13px 0 0;
        }

        .markdown-body span.float-right {
            display: block;
            float: right;
            margin-left: 13px;
            overflow: hidden;
        }

        .markdown-body span.float-right>span {
            display: block;
            margin: 13px auto 0;
            overflow: hidden;
            text-align: right;
        }

        .markdown-body code,
        .markdown-body tt {
            padding: .2em .4em;
            margin: 0;
            font-size: 85%;
            white-space: break-spaces;
            background-color: #818b981f;
            border-radius: 6px;
        }

        .markdown-body code br,
        .markdown-body tt br {
            display: none;
        }

        .markdown-body del code {
            text-decoration: inherit;
        }

        .markdown-body samp {
            font-size: 85%;
        }

        .markdown-body pre code {
            font-size: 100%;
        }

        .markdown-body pre>code {
            padding: 0;
            margin: 0;
            word-break: normal;
            white-space: pre;
            background: transparent;
            border: 0;
        }

        .markdown-body .highlight {
            margin-bottom: 1rem;
        }

        .markdown-body .highlight pre {
            margin-bottom: 0;
            word-break: normal;
        }

        .markdown-body .highlight pre,
        .markdown-body pre {
            padding: 1rem;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            color: #1f2328;
            background-color: #f6f8fa;
            border-radius: 6px;
        }

        .markdown-body pre code,
        .markdown-body pre tt {
            display: inline;
            max-width: auto;
            padding: 0;
            margin: 0;
            overflow: visible;
            line-height: inherit;
            word-wrap: normal;
            background-color: transparent;
            border: 0;
        }

        .markdown-body .csv-data td,
        .markdown-body .csv-data th {
            padding: 5px;
            overflow: hidden;
            font-size: 12px;
            line-height: 1;
            text-align: left;
            white-space: nowrap;
        }

        .markdown-body .csv-data .blob-num {
            padding: 10px 0.5rem 9px;
            text-align: right;
            background: #ffffff;
            border: 0;
        }

        .markdown-body .csv-data tr {
            border-top: 0;
        }

        .markdown-body .csv-data th {
            font-weight: 600;
            background: #f6f8fa;
            border-top: 0;
        }

        .markdown-body [data-footnote-ref]::before {
            content: "[";
        }

        .markdown-body [data-footnote-ref]::after {
            content: "]";
        }

        .markdown-body .footnotes {
            font-size: 12px;
            color: #59636e;
            border-top: 1px solid #d1d9e0;
        }

        .markdown-body .footnotes ol {
            padding-left: 1rem;
        }

        .markdown-body .footnotes ol ul {
            display: inline-block;
            padding-left: 1rem;
            margin-top: 1rem;
        }

        .markdown-body .footnotes li {
            position: relative;
        }

        .markdown-body .footnotes li:target::before {
            position: absolute;
            top: calc(0.5rem*-1);
            right: calc(0.5rem*-1);
            bottom: calc(0.5rem*-1);
            left: calc(1.5rem*-1);
            pointer-events: none;
            content: "";
            border: 2px solid #0969da;
            border-radius: 6px;
        }

        .markdown-body .footnotes li:target {
            color: #1f2328;
        }

        .markdown-body .footnotes .data-footnote-backref g-emoji {
            font-family: monospace;
        }

        .markdown-body body:has(:modal) {
            padding-right: var(--dialog-scrollgutter) !important;
        }

        .markdown-body .pl-c {
            color: #59636e;
        }

        .markdown-body .pl-c1,
        .markdown-body .pl-s .pl-v {
            color: #0550ae;
        }

        .markdown-body .pl-e,
        .markdown-body .pl-en {
            color: #6639ba;
        }

        .markdown-body .pl-smi,
        .markdown-body .pl-s .pl-s1 {
            color: #1f2328;
        }

        .markdown-body .pl-ent {
            color: #0550ae;
        }

        .markdown-body .pl-k {
            color: #cf222e;
        }

        .markdown-body .pl-s,
        .markdown-body .pl-pds,
        .markdown-body .pl-s .pl-pse .pl-s1,
        .markdown-body .pl-sr,
        .markdown-body .pl-sr .pl-cce,
        .markdown-body .pl-sr .pl-sre,
        .markdown-body .pl-sr .pl-sra {
            color: #0a3069;
        }

        .markdown-body .pl-v,
        .markdown-body .pl-smw {
            color: #953800;
        }

        .markdown-body .pl-bu {
            color: #82071e;
        }

        .markdown-body .pl-ii {
            color: #f6f8fa;
            background-color: #82071e;
        }

        .markdown-body .pl-c2 {
            color: #f6f8fa;
            background-color: #cf222e;
        }

        .markdown-body .pl-sr .pl-cce {
            font-weight: bold;
            color: #116329;
        }

        .markdown-body .pl-ml {
            color: #3b2300;
        }

        .markdown-body .pl-mh,
        .markdown-body .pl-mh .pl-en,
        .markdown-body .pl-ms {
            font-weight: bold;
            color: #0550ae;
        }

        .markdown-body .pl-mi {
            font-style: italic;
            color: #1f2328;
        }

        .markdown-body .pl-mb {
            font-weight: bold;
            color: #1f2328;
        }

        .markdown-body .pl-md {
            color: #82071e;
            background-color: #ffebe9;
        }

        .markdown-body .pl-mi1 {
            color: #116329;
            background-color: #dafbe1;
        }

        .markdown-body .pl-mc {
            color: #953800;
            background-color: #ffd8b5;
        }

        .markdown-body .pl-mi2 {
            color: #d1d9e0;
            background-color: #0550ae;
        }

        .markdown-body .pl-mdr {
            font-weight: bold;
            color: #8250df;
        }

        .markdown-body .pl-ba {
            color: #59636e;
        }

        .markdown-body .pl-sg {
            color: #818b98;
        }

        .markdown-body .pl-corl {
            text-decoration: underline;
            color: #0a3069;
        }

        .markdown-body [role=button]:focus:not(:focus-visible),
        .markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),
        .markdown-body button:focus:not(:focus-visible),
        .markdown-body summary:focus:not(:focus-visible),
        .markdown-body a:focus:not(:focus-visible) {
            outline: none;
            box-shadow: none;
        }

        .markdown-body [tabindex="0"]:focus:not(:focus-visible),
        .markdown-body details-dialog:focus:not(:focus-visible) {
            outline: none;
        }

        .markdown-body g-emoji {
            display: inline-block;
            min-width: 1ch;
            font-family: "Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
            font-size: 1em;
            font-style: normal !important;
            font-weight: 400;
            line-height: 1;
            vertical-align: -0.075em;
        }

        .markdown-body g-emoji img {
            width: 1em;
            height: 1em;
        }

        .markdown-body .task-list-item {
            list-style-type: none;
        }

        .markdown-body .task-list-item label {
            font-weight: 400;
        }

        .markdown-body .task-list-item.enabled label {
            cursor: pointer;
        }

        .markdown-body .task-list-item+.task-list-item {
            margin-top: 0.25rem;
        }

        .markdown-body .task-list-item .handle {
            display: none;
        }

        .markdown-body .task-list-item-checkbox {
            margin: 0 .2em .25em -1.4em;
            vertical-align: middle;
        }

        .markdown-body ul:dir(rtl) .task-list-item-checkbox {
            margin: 0 -1.6em .25em .2em;
        }

        .markdown-body ol:dir(rtl) .task-list-item-checkbox {
            margin: 0 -1.6em .25em .2em;
        }

        .markdown-body .contains-task-list:hover .task-list-item-convert-container,
        .markdown-body .contains-task-list:focus-within .task-list-item-convert-container {
            display: block;
            width: auto;
            height: 24px;
            overflow: visible;
            clip: auto;
        }

        .markdown-body ::-webkit-calendar-picker-indicator {
            filter: invert(50%);
        }

        .markdown-body .markdown-alert {
            padding: 0.5rem 1rem;
            margin-bottom: 1rem;
            color: inherit;
            border-left: .25em solid #d1d9e0;
        }

        .markdown-body .markdown-alert>:first-child {
            margin-top: 0;
        }

        .markdown-body .markdown-alert>:last-child {
            margin-bottom: 0;
        }

        .markdown-body .markdown-alert .markdown-alert-title {
            display: flex;
            font-weight: 500;
            align-items: center;
            line-height: 1;
        }

        .markdown-body .markdown-alert.markdown-alert-note {
            border-left-color: #0969da;
        }

        .markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {
            color: #0969da;
        }

        .markdown-body .markdown-alert.markdown-alert-important {
            border-left-color: #8250df;
        }

        .markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {
            color: #8250df;
        }

        .markdown-body .markdown-alert.markdown-alert-warning {
            border-left-color: #9a6700;
        }

        .markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {
            color: #9a6700;
        }

        .markdown-body .markdown-alert.markdown-alert-tip {
            border-left-color: #1a7f37;
        }

        .markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {
            color: #1a7f37;
        }

        .markdown-body .markdown-alert.markdown-alert-caution {
            border-left-color: #cf222e;
        }

        .markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {
            color: #d1242f;
        }

        .markdown-body>*:first-child>.heading-element:first-child {
            margin-top: 0 !important;
        }

        .markdown-body .highlight pre:has(+.zeroclipboard-container) {
            min-height: 52px;
        }
    </style>
</head>
<body class="markdown-body">
<h3 id="termsofservice"><strong>Terms of Service</strong></h3>
<p>These Terms of Service ("Terms") govern your access to and use of the <strong>ChatDOC Studio platform and services</strong> (collectively, the "Service"), provided by ChatDOC Studio ("we," "us," or "our").</p>
<p>You can contact us by email at <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><EMAIL></a></p>
<p>These Legal Terms constitute a legally binding agreement made between you, whether personally or on behalf of an entity ("<strong>you</strong>"), and ChatDOC Studio, concerning your access to and use of the Services. You agree that by accessing the Services, you have read, understood, and agreed to be bound by all of these Legal Terms. IF YOU DO NOT AGREE WITH ALL OF THESE LEGAL TERMS, THEN YOU ARE EXPRESSLY PROHIBITED FROM USING THE SERVICES AND YOU MUST DISCONTINUE USE IMMEDIATELY.</p>
<p>Supplemental terms and conditions or documents that may be posted on the Services from time to time are hereby expressly incorporated herein by reference. We reserve the right, in our sole discretion, to make changes or modifications to these Legal Terms from time to time . We will alert you about any changes by updating the "Last updated" date of these Legal Terms, and you waive any right to receive specific notice of each such change. It is your responsibility to periodically review these Legal Terms to stay informed of updates. You will be subject to, and will be deemed to have been made aware of and to have accepted, the changes in any revised Legal Terms by your continued use of the Services after the date such revised Legal Terms are posted.</p>
<p><strong>TABLE OF CONTENTS</strong></p>
<p><a href="#DescriptionOfService" rel="noopener noreferrer">1. Description of Service</a></p>
<p><a href="#AccountAndResponsibilities" rel="noopener noreferrer">2. Your Account and Responsibilities</a></p>
<p><a href="#AcceptableUse" rel="noopener noreferrer">3. Acceptable Use</a></p>
<p><a href="#IntellectualProperty" rel="noopener noreferrer">4. Intellectual Property</a></p>
<p><a href="#FeesAndPayment" rel="noopener noreferrer">5. Fees and Payment</a></p>
<p><a href="#DisclaimerOfWarranties" rel="noopener noreferrer">6. Disclaimer of Warranties</a></p>
<p><a href="#LimitationOfLiability" rel="noopener noreferrer">7. Limitation of Liability</a></p>
<p><a href="#Indemnification" rel="noopener noreferrer">8. Indemnification</a></p>
<p><a href="#Termination" rel="noopener noreferrer">9. Termination</a></p>
<p><a href="#ChangesToTerms" rel="noopener noreferrer">10. Changes to Terms</a></p>
<p><a href="#GoverningLaw" rel="noopener noreferrer">11. Governing Law</a></p>
<p id="DescriptionOfService"><strong>1. Description of Service</strong></p>
<p>ChatDOC Studio is a platform that allows users ("Creators," "you") to upload documents and other content ("User Content") to build, customize, and deploy AI-powered question-answering applications and knowledge bots ("<strong>**AI</strong> <strong>Applications").</strong></p>
<p id="AccountAndResponsibilities"><strong>2. Your Account and Responsibilities</strong></p>
<ul>
    <li><p>Eligibility: You must be at least 18 years old to use the Service.</p></li>
    <li><p>Account Security: You are responsible for safeguarding your account password and for all activities that occur under your account. You must notify us immediately of any unauthorized use of your account.</p></li>
    <li><p><strong>Your Responsibilities as a Creator: You are solely responsible for your User Content and the</strong> <strong>AI</strong> <strong>Applications you create. You agree that you have all necessary rights to the User Content you upload and that your use of the Service and the behavior of your AI Applications will comply with all applicable laws and regulations.</strong></p></li>
</ul>
<p id="AcceptableUse"><strong>3. Acceptable Use</strong></p>
<p>You agree not to use the Service to:</p>
<ul>
    <li><p>Upload or process any content that is illegal, harmful, threatening, abusive, or otherwise objectionable.</p></li>
    <li><p><strong>Create AI Applications that generate misinformation, engage in hate speech, or promote violence.</strong></p></li>
    <li><p><strong>Develop AI Applications intended to provide unlicensed medical, legal, financial, or other professional advice.</strong></p></li>
    <li><p>Infringe upon the intellectual property rights of others.</p></li>
    <li><p>Violate the privacy of any third party.</p></li>
    <li><p>Attempt to disrupt or compromise the integrity or security of the Service.</p></li>
</ul>
<p id="IntellectualProperty"><strong>4. Intellectual Property</strong></p>
<ul>
    <li><p>Your Property: You retain all ownership rights to the User Content you upload to the Service. You also own the specific AI Applications you create.</p></li>
    <li><p>Our Property: We and our licensors retain all ownership rights to the Service itself, including the underlying platform, software, AI models, and all related technology and documentation.</p></li>
    <li><p>License to Us: By using the Service, you grant us a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, and process your User Content solely for the purpose of providing and improving the Service for you.</p></li>
</ul>
<p id="FeesAndPayment"><strong>5. Fees and Payment</strong></p>
<p>Certain features of the Service may be provided for a fee. You agree to pay all applicable fees for the subscription plan you select. All fees are non-refundable except as required by law. We reserve the right to change our prices and will notify you in advance of any such changes.</p>
<p id="DisclaimerOfWarranties"><strong>6. Disclaimer of Warranties</strong></p>
<p>THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED. WE DO NOT WARRANT THAT THE SERVICE WILL BE UNINTERRUPTED, ERROR-FREE, OR COMPLETELY SECURE. THE ANSWERS GENERATED BY YOUR AI APPLICATIONS ARE BASED ON THE USER CONTENT YOU PROVIDE AND ARE FOR INFORMATIONAL PURPOSES ONLY. YOU ARE RESPONSIBLE FOR VERIFYING THE ACCURACY AND RELIABILITY OF ANY OUTPUT.</p>
<p id="LimitationOfLiability"><strong>7. Limitation of Liability</strong></p>
<p>TO THE FULLEST EXTENT PERMITTED BY LAW, IN NO EVENT SHALL CHATDOC STUDIO BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, OR ANY LOSS OF PROFITS OR REVENUES, WHETHER INCURRED DIRECTLY OR INDIRECTLY, OR ANY LOSS OF DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR USE OF THE SERVICE.</p>
<p id="Indemnification"><strong>8. Indemnification</strong></p>
<p>You agree to indemnify and hold harmless ChatDOC Studio and its employees from and against any claims, liabilities, damages, and costs (including reasonable attorney's fees) arising out of your use of the Service, your violation of these Terms, or <strong>any claim from an end-user of your</strong> <strong>AI</strong> <strong>Applications.</strong></p>
<p id="Termination"><strong>9. Termination</strong></p>
<p>We may suspend or terminate your access to the Service at any time, with or without cause. You may terminate your account at any time by contacting us or through your account settings.</p>
<p id="ChangesToTerms"><strong>10. Changes to Terms</strong></p>
<p>We may modify these Terms from time to time. We will provide you with notice of any material changes. Your continued use of the Service after the changes become effective constitutes your acceptance of the new Terms.</p>
<p id="GoverningLaw"><strong>11. Governing Law</strong></p>
<p>These Terms shall be governed by the laws of [Your Jurisdiction], without regard to its conflict of law provisions.</p>
</body>
</html>