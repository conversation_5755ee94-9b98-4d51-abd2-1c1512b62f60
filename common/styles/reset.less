html {
  color: #2c3e50;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0%);
}

body {
  font-size: 16px;
  line-height: 1.666;
}

html,
body,
header,
footer,
section,
aside,
article,
nav,
hgroup,
figure,
figcaption,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
ol,
li,
form,
p,
dl,
dt,
dd,
blockquote,
legend,
table,
th,
td,
fieldset,
menu,
pre {
  margin: 0;
  padding: 0;
}

img,
fieldset {
  border: 0;
}

time,
mark,
output,
meter,
address,
cite,
em,
code,
var,
dfn,
ins,
i,
th,
caption {
  font-style: normal;
  text-decoration: none;
}

input,
select,
button,
textarea,
table {
  margin: 0;
  font-size: 100%;
  font-family: inherit;
}

button:focus {
  outline: none;
}

abbr,
acronym {
  font-variant: normal;
  border: 0;
}

ul,
ol {
  list-style: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

th,
caption {
  text-align: left;
}

header,
footer,
section,
aside,
article,
nav,
hgroup,
figure,
figcaption {
  display: block;
}

a {
  color: rgba(75, 146, 255, 100%);
  text-decoration: none;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0%);
}

a:hover {
  text-decoration: none;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

// 垂直方向的carousel
.carousel-vertically {
  .carousel-item-next:not(.carousel-item-start),
  .active.carousel-item-end {
    transform: translateY(100%);
  }

  .carousel-item-prev:not(.carousel-item-end),
  .active.carousel-item-start {
    transform: translateY(-100%);
  }
}

// Toastify提示框
.Toastify {
  .Toastify__toast-container {
    z-index: 9999;
    margin: 12vh auto;

    @media (max-width: 480px) {
      width: 80vw;
      margin-left: calc((100vw - 80vw) / 2);
    }
  }
}

// dropdown展开收起时的箭头
.dropdown-has-arrow {
  .dropdown-toggle {
    &::after {
      width: 9px;
      height: 9px;
      margin-top: 3px;
      margin-left: auto;
      border: solid @dark-color;
      border-width: 2px 2px 0 0;
      transform: matrix(-0.71, 0.71, -0.71, -0.71, 0, 0);
      content: '';
    }

    &.show {
      &::after {
        transform: matrix(-0.71, -0.71, -0.71, 0.71, 0, 0);
      }
    }
  }
}

// 当前激活的list项
.list-item-active {
  --list-item-selected-offset-left: -40px;

  @media (max-width: @mini-screen-size) {
    --list-item-selected-offset-left: -30px;
  }

  position: relative;

  .list-item-selected {
    position: absolute;
    left: var(--list-item-selected-offset-left);
    width: calc(100% + (-1 * var(--list-item-selected-offset-left)));
    height: 100%;
    background-color: rgba(101, 118, 219, 10%);
    border-radius: 30px 0 0 30px;

    @media (max-width: @mini-screen-size) {
      border-radius: 35px 0 0 35px;
    }
  }

  .list-item-text {
    color: #6576db !important;
  }
}

// 默认button样式
.default-btn {
  display: inline-block;
  padding: 7px 24px;
  color: #fff;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  white-space: nowrap;
  text-align: center;
  background-color: @primary-color;
  border: 1px solid @primary-color;
  border-radius: @border-radius-base;
  cursor: pointer;

  &:hover {
    color: #fff;
  }

  &:disabled {
    opacity: 0.65;
  }
}

/*
  Font: Poppins
  Copyright 2020 The Poppins Project Authors (https://github.com/itfoundry/Poppins)
  License: SIL Open Font License, Version 1.1 (http://scripts.sil.org/OFL)
*/
@font-face {
  font-weight: 700;
  font-family: Poppins;
  font-style: normal;
  src:
    local('Poppins-Bold'),
    /* 如果用户系统中已经安装了这个字体，则使用本地字体 */
      url('../static/font/Poppins-Bold.woff2') format('woff2'),
    url('../static/font/Poppins-Bold.ttf') format('truetype');
  font-display: swap;
}

@font-face {
  font-weight: 600;
  font-family: Poppins;
  font-style: normal;
  src:
    local('Poppins-SemiBold'),
    url('../static/font/Poppins-SemiBold.woff2') format('woff2'),
    url('../static/font/Poppins-SemiBold.ttf') format('truetype');
  font-display: swap;
}

@font-face {
  font-weight: 400;
  font-family: Poppins;
  font-style: normal;
  src:
    local('Poppins-Regular'),
    url('../static/font/Poppins-Regular.woff2') format('woff2'),
    url('../static/font/Poppins-Regular.ttf') format('truetype');
  font-display: swap;
}

@font-face {
  font-weight: 500;
  font-family: Poppins;
  font-style: normal;
  src:
    local('Poppins-Medium'),
    url('../static/font/Poppins-Medium.woff2') format('woff2'),
    url('../static/font/Poppins-Medium.ttf') format('truetype');
  font-display: swap;
}
