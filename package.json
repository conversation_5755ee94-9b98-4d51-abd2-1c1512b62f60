{"name": "paoding-official-web", "private": true, "description": "Paoding official web(static pages)", "version": "0.1.0", "workspaces": ["sites/*"], "scripts": {"dev:autodoc": "yarn workspace autodoc develop", "dev:autodoc-vip": "yarn workspace autodoc develop-vip", "dev:autodoc-next": "yarn workspace autodoc develop-next", "build:autodoc": "yarn workspace autodoc build", "build:autodoc-test": "yarn workspace autodoc build-test", "build:autodoc-vip": "yarn workspace autodoc build-vip", "build:autodoc-vip-test": "yarn workspace autodoc build-vip-test", "build:autodoc-next": "yarn workspace autodoc build-next", "build:autodoc-next-test": "yarn workspace autodoc build-next-test", "lint:autodoc": "yarn workspace autodoc lint", "dev:calliper": "yarn workspace calliper develop", "dev:calliper-vip": "yarn workspace calliper develop-vip", "build:calliper": "yarn workspace calliper build", "build:calliper-test": "yarn workspace calliper build-test", "build:calliper-vip": "yarn workspace calliper build-vip", "build:calliper-vip-test": "yarn workspace calliper build-vip-test", "lint:calliper": "yarn workspace calliper lint", "dev:chatdoc": "yarn workspace chatdoc develop", "build:chatdoc": "yarn workspace chatdoc build", "build:chatdoc-staging": "yarn workspace chatdoc build-staging", "build:chatdoc-test": "yarn workspace chatdoc build-test", "build:chatdoc-cdn": "yarn workspace chatdoc build-cdn", "build:chatdoc-cdn-staging": "yarn workspace chatdoc build-cdn-staging", "build:chatdoc-cdn-test": "yarn workspace chatdoc build-cdn-test", "serve:chatdoc": "yarn workspace chatdoc serve", "lint:chatdoc": "yarn workspace chatdoc lint", "dev:chatdoc-studio": "yarn workspace chatdoc-studio develop", "build:chatdoc-studio": "yarn workspace chatdoc-studio build", "build:chatdoc-studio-staging": "yarn workspace chatdoc-studio build-staging", "build:chatdoc-studio-test": "yarn workspace chatdoc-studio build-test", "build:chatdoc-studio-cdn": "yarn workspace chatdoc-studio build-cdn", "build:chatdoc-studio-cdn-staging": "yarn workspace chatdoc-studio build-cdn-staging", "build:chatdoc-studio-cdn-test": "yarn workspace chatdoc-studio build-cdn-test", "serve:chatdoc-studio": "yarn workspace chatdoc-studio serve", "lint:chatdoc-studio": "yarn workspace chatdoc-studio lint", "dev:chatpaper": "yarn workspace chatpaper develop", "build:chatpaper": "yarn workspace chatpaper build", "build:chatpaper-staging": "yarn workspace chatpaper build-staging", "build:chatpaper-test": "yarn workspace chatpaper build-test", "build:chatpaper-cdn": "yarn workspace chatpaper build-cdn", "build:chatpaper-cdn-staging": "yarn workspace chatpaper build-cdn-staging", "build:chatpaper-cdn-test": "yarn workspace chatpaper build-cdn-test", "lint:chatpaper": "yarn workspace chatpaper lint", "serve:chatpaper": "yarn workspace chatpaper serve", "dev:pdfchat": "yarn workspace pdfchat develop", "build:pdfchat": "yarn workspace pdfchat build", "build:pdfchat-test": "yarn workspace pdfchat build-test", "build:pdfchat-cdn": "yarn workspace pdfchat build-cdn", "build:pdfchat-cdn-test": "yarn workspace pdfchat build-cdn-test", "lint:pdfchat": "yarn workspace pdfchat lint", "dev:grater": "yarn workspace grater develop", "dev:grater-vip": "yarn workspace grater develop-vip", "build:grater": "yarn workspace grater build", "build:grater-test": "yarn workspace grater build-test", "build:grater-vip": "yarn workspace grater build-vip", "build:grater-vip-test": "yarn workspace grater build-vip-test", "lint:grater": "yarn workspace grater lint", "dev:glazer": "yarn workspace glazer develop", "build:glazer": "yarn workspace glazer build", "build:glazer-test": "yarn workspace glazer build-test", "lint:glazer": "yarn workspace glazer lint", "dev:pdfparser": "yarn workspace pdfparser develop", "build:pdfparser": "yarn workspace pdfparser build", "build:pdfparser-staging": "yarn workspace pdfparser build-staging", "build:pdfparser-test": "yarn workspace pdfparser build-test", "serve:pdfparser": "yarn workspace pdfparser serve", "lint:pdfparser": "yarn workspace pdfparser lint", "dev:paodingjiewen": "yarn workspace paodingjiewen develop", "build:paodingjiewen": "yarn workspace paodingjiewen build", "build:paodingjiewen-test": "yarn workspace paodingjiewen build-test", "lint:paodingjiewen": "yarn workspace paodingjiewen lint", "dev:pdflux": "yarn workspace pdflux develop", "build:pdflux": "yarn workspace pdflux build", "build:pdflux-test": "yarn workspace pdflux build-test", "build:pdflux-cdn": "yarn workspace pdflux build-cdn", "build:pdflux-cdn-test": "yarn workspace pdflux build-cdn-test", "lint:pdflux": "yarn workspace pdflux lint", "dev:pdflux-sdk": "yarn workspace pdflux-sdk develop", "build:pdflux-sdk": "yarn workspace pdflux-sdk build", "build:pdflux-sdk-test": "yarn workspace pdflux-sdk build-test", "lint:pdflux-sdk": "yarn workspace pdflux-sdk lint", "dev:paodingai": "yarn workspace paodingai develop", "build:paodingai": "yarn workspace paodingai build", "build:paodingai-test": "yarn workspace paodingai build-test", "serve:paodingai": "yarn workspace paodingai serve", "lint:paodingai": "yarn workspace paodingai lint", "dev:chatdb": "yarn workspace chatdb develop", "build:chatdb": "yarn workspace chatdb build", "build:chatdb-test": "yarn workspace chatdb build-test", "lint:chatdb": "yarn workspace chatdb lint", "dev:scriber": "yarn workspace scriber develop", "build:scriber": "yarn workspace scriber build", "build:scriber-test": "yarn workspace scriber build-test", "lint:scriber": "yarn workspace scriber lint", "dev:hunter": "yarn workspace hunter develop", "build:hunter": "yarn workspace hunter build", "build:hunter-test": "yarn workspace hunter build-test", "lint:hunter": "yarn workspace hunter lint", "dev:semanmeter": "yarn workspace semanmeter develop", "build:semanmeter": "yarn workspace semanmeter build", "build:semanmeter-test": "yarn workspace semanmeter build-test", "lint:semanmeter": "yarn workspace semanmeter lint", "dev:online-soon": "yarn workspace online-soon develop", "build:online-soon": "yarn workspace online-soon build", "build:online-soon-test": "yarn workspace online-soon build-test", "lint:online-soon": "yarn workspace online-soon lint", "dev:support": "yarn workspace support develop", "build:support": "yarn workspace support build", "build:support-test": "yarn workspace support build-test", "lint:support": "yarn workspace support lint", "dev:chatitdone": "yarn workspace chatitdone develop", "build:chatitdone": "yarn workspace chatitdone build", "build:chatitdone-test": "yarn workspace chatitdone build-test", "lint:chatitdone": "yarn workspace chatitdone lint", "dev:metalmesh": "yarn workspace metalmesh develop", "build:metalmesh": "yarn workspace metalmesh build", "build:metalmesh-test": "yarn workspace metalmesh build-test", "lint:metalmesh": "yarn workspace metalmesh lint", "lint": "eslint --fix \"{sites/*/src,common}/**/*.{js,jsx}\"", "lint:stylelint": "stylelint --fix \"{sites/*/src,common}/**/*.{less,css,scss}\"", "format": "prettier --write \"{sites/*/src,common}/**/*.{js,jsx,less,css,scss}\"", "prepare": "husky install", "clean": "gatsby clean"}, "dependencies": {"@deckdeckgo/highlight-code": "^4.4.0", "@emotion/react": "^11.10.6", "@loadable/component": "^5.16.4", "@paoding/customer-service": "^0.3.13", "@reach/router": "^1.3.4", "aes-js": "^3.1.2", "animate.css": "^4.1.1", "axios": "^0.27.2", "base64-js": "^1.5.1", "bootstrap": "^5.2.3", "classnames": "^2.3.2", "clipboard": "^2.0.11", "cryptjs": "^1.0.1", "dayjs": "^1.11.7", "deepmerge": "^4.3.1", "eslint-plugin-react-hooks": "^4.6.0", "fontfaceobserver": "^2.3.0", "formik": "^2.2.9", "gatsby": "^5.8.0", "gatsby-plugin-baidu-analytics": "^2.2.0", "gatsby-plugin-clarity": "^1.0.1", "gatsby-plugin-copy-files": "^1.1.0", "gatsby-plugin-google-analytics": "^5.8.0", "gatsby-plugin-google-gtag": "^4.25.0", "gatsby-plugin-google-tagmanager": "^5.13.1", "gatsby-plugin-image": "^3.8.0", "gatsby-plugin-less": "^7.8.0 ", "gatsby-plugin-manifest": "^5.8.0", "gatsby-plugin-no-sourcemaps": "^5.8.0", "gatsby-plugin-offline": "^6.8.0", "gatsby-plugin-page-creator": "^5.8.0", "gatsby-plugin-polyfill-io": "^1.1.0", "gatsby-plugin-postcss": "^6.8.0", "gatsby-plugin-react-svg": "^3.3.0", "gatsby-plugin-remove-serviceworker": "^1.0.0", "gatsby-plugin-robots-txt": "^1.8.0", "gatsby-plugin-sharp": "^5.8.0", "gatsby-plugin-sitemap": "^ 6.8.0 ", "gatsby-plugin-sitemap-ehd": "5.20.3-ehd", "gatsby-remark-external-links": "^0.0.4", "gatsby-remark-highlight-code": "^3.3.0", "gatsby-remark-images": "^7.8.0 ", "gatsby-source-filesystem": "^5.8.0", "gatsby-source-local-git": "^1.3.0", "gatsby-source-strapi": "^3.1.3", "gatsby-transformer-json": "^5.8.0", "gatsby-transformer-remark": "^6.8.0", "gatsby-transformer-sharp": "^5.8.0", "http-proxy-middleware": "^2.0.6", "intersection-observer": "^0.12.2", "intl": "^1.2.5", "js-base64": "^3.7.5", "js-cookie": "^3.0.1", "less": "^4.1.3", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "mitt": "^3.0.1", "pako": "^2.1.0", "postcss-calc": "^8.2.4", "postcss-nested": "^6.0.1", "postcss-preset-env": "^8.1.0", "prop-types": "^15.8.1", "qs": "^6.11.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-contenteditable": "^3.3.7", "react-countup": "^6.4.2", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-highlight": "^0.15.0", "react-in-viewport": "^1.0.0-alpha.28", "react-intl": "^6.3.2", "react-lazyload": "^3.2.0", "react-markdown": "^8.0.7", "react-odometerjs": "^3.1.3", "react-scroll": "^1.8.9", "react-select": "^5.7.2", "react-toastify": "^9.0.8", "swiper": "^8.4.2", "ua-parser-js": "^1.0.34", "unsupported-browser-plugin": "0.1.2", "uuid": "^11.0.5", "vanilla-lazyload": "^19.1.3", "xgplayer": "^3.0.11", "yup": "^0.32.11"}, "devDependencies": {"@paoding/prettier-config": "^1.0.1", "cross-env": "^7.0.3", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "gatsby-plugin-perf-budgets": "^0.0.18", "gatsby-plugin-webpack-bundle-analyser-v2": "^1.1.32", "husky": "^8.0.3", "lint-staged": "^13.2.0", "prettier": "^3.1.1", "stylelint": "^16.1.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard-scss": "^13.0.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.0", "vconsole": "^3.15.0"}, "lint-staged": {"{sites/*/src,common}/**/*.{js,jsx}": ["eslint --fix", "prettier --write"], "{sites/*/src,common}/**/*.{less,css,scss}": ["prettier --write", "stylelint --fix"]}}