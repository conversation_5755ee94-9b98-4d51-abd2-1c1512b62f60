#!/usr/bin/env bash

set -ex
set -o pipefail

rm -rf sites/chatdoc-studio/public* public*

yarn install

# BUILD_POSTFIX set by gocd, default build for prod. if build for test, add BUILD_POSTFIX=-test on gocd.
yarn build:chatdoc-studio"${BUILD_POSTFIX}"
gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/chatdoc-studio/public
mv sites/chatdoc-studio/public public-chatdoc-studio

if [[ -n "${ENABLE_CHATDOC_STUDIO_CDN}" ]]; then
  yarn build:chatdoc-studio-cdn"${BUILD_POSTFIX}"
  # replace https://cdn.pdppt.com/chatdoc-studio/page-data/app-data.json to /page-data/app-data.json
  ls -1 sites/chatdoc-studio/public/app-*.js | xargs -I {} sed -i "s@https://cdn.pdppt.com/chatdoc-studio/page-data@/page-data@g" {}

  # add args for app.js
  ts=$(date +%s)
  find sites/chatdoc-studio/public -name '*.html' -type f | xargs -I {} sed -Ei \
    's@https://cdn.pdppt.com/chatdoc-studio/app-(.*?).js@https://cdn.pdppt.com/chatdoc-studio/app-\1.js?_t='"${ts}"'@g' {}

  gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/chatdoc-studio/public

  mv sites/chatdoc-studio/public public-chatdoc-studio-cdn
fi
